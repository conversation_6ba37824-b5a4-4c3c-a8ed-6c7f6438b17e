const express = require('express');
const http = require('http');
const cors = require('cors');
const dotenv = require('dotenv');
const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Import configurations
const connectDB = require('./config/db');
const { connectRedis, closeRedisConnection } = require('./config/redis');
const { initializeSocket } = require('./config/socket');
const socketHandlers = require('./utils/socketHandlers');
const { apiLimiter } = require('./middleware/rateLimiter');

// Load environment variables
dotenv.config();

// Create Express app and HTTP server
const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 5000;

// Connect to databases
connectDB();
connectRedis();

// Middleware
app.use(cors());
app.use(express.json({ limit: '2gb' }));
app.use(express.urlencoded({ limit: '2gb', extended: true }));
app.use(apiLimiter); // Apply rate limiter to all routes

// Routes
app.get('/', (req, res) => {
	res.send('Welcome to the You = Life platform API');
});

// API Routes
app.use('/api/auth', require('./routes/authRoutes'));
app.use('/api/user', require('./routes/userRoutes'));
app.use('/api/lesson', require('./routes/lessonRoutes'));
app.use('/api/notification', require('./routes/notificationRoutes'));
app.use('/api/socket', require('./routes/socket'));
app.use('/api/goals', require('./routes/goalRoutes'));
app.use('/api/values', require('./routes/valueRoutes'));
app.use('/api/diary', require('./routes/diaryRoutes'));
app.use('/api/user', require('./routes/userProgressRoutes'));

// Initialize Socket.IO AFTER setting up routes
const io = initializeSocket(server);
socketHandlers.initializeHandlers(io);

// Swagger configuration
const swaggerOptions = {
	definition: {
		openapi: '3.0.0',
		info: {
			title: 'You = Life API',
			version: '1.0.0',
			description: 'API documentation for the You = Life platform',
		},
		servers: [
			{
				url: 'https://you-life-server.onrender.com/',
				description: 'Production server',
			},
			{
				url: 'http://localhost:5000',
				description: 'Development server',
			},
		],
	},
	apis: ['./routes/*.js'], // Path to the route files
};

const swaggerDocs = swaggerJsDoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// Start the server
server.listen(PORT, () => {
	console.log(`Server is running on http://localhost:${PORT}`);
	console.log(`Socket.IO server ready to accept connections`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
	console.log('Shutting down server...');

	// Close Socket.IO connections
	if (io) {
		io.close(() => {
			console.log('Socket.IO server closed');
		});
	}

	server.close(() => {
		closeRedisConnection().finally(() => {
			console.log('Server and Redis connection closed.');
			process.exit(0);
		});
	});
});

process.on('SIGTERM', async () => {
	console.log('SIGTERM received, shutting down gracefully...');

	// Close Socket.IO connections
	if (io) {
		io.close();
	}

	server.close(() => {
		closeRedisConnection().finally(() => {
			console.log('Server and Redis connection closed.');
			process.exit(0);
		});
	});
});
