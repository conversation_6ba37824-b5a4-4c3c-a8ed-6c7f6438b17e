import React from 'react';
import { TbLogout } from 'react-icons/tb';
import { useRouter } from 'next/navigation';

const clearUserData = () => {
	// Clear localStorage and sessionStorage
	if (typeof window !== 'undefined') {
		localStorage.clear();
		sessionStorage.clear();
	}
	// Optionally clear cookies if used (example for JWT)
	document.cookie.split(';').forEach((c) => {
		document.cookie = c
			.replace(/^ +/, '')
			.replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
	});
};

const SignOutConfirmModal = ({ onCancel, onConfirm }) => {
	const router = useRouter();

	const handleConfirm = () => {
		clearUserData();
		if (onConfirm) onConfirm();
		router.push('/auth/login'); // Redirect to login page
	};

	return (
		<div className='fixed inset-0 flex items-center justify-center z-50'>
			<div className='bg-white rounded-lg p-6 shadow-lg'>
				<div className='flex justify-center mb-4'>
					<TbLogout className='w-12 h-12 text-black' />
				</div>
				<p className='text-center text-gray-800 mb-6'>
					Are you sure you want to sign out?
				</p>
				<div className='flex justify-between'>
					<button
						onClick={onCancel}
						className='px-4 py-2 border-2 border-gray-700 rounded-md text-gray-700 hover:bg-gray-100'>
						Cancel
					</button>
					<button
						onClick={handleConfirm}
						className='px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700'>
						Yes, Sign out
					</button>
				</div>
			</div>
		</div>
	);
};

export default SignOutConfirmModal;
