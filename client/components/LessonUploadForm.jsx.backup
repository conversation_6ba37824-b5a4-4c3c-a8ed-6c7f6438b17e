'use client';
import { useState, useCallback, useMemo, memo } from 'react';
import { useRouter } from 'next/navigation';
import { useUploadLesson, useLessonCategories } from '@/hooks/useLessons';
import {
	FaUpload,
	FaArrowLeft,
	FaCheckCircle,
	FaExclamationTriangle,
} from 'react-icons/fa';
import AdminLayout from './AdminLayout';
import CloudinaryUpload from './CloudinaryUpload';

// Memoized components to prevent unnecessary re-renders
const ProgressBar = memo(({ uploadState }) => (
	<div className='space-y-4'>
		<div className='flex items-center justify-between'>
			<span className='text-sm font-medium text-gray-700'>
				{uploadState.currentStep}
			</span>
			<span className='text-sm text-gray-500'>{uploadState.progress}%</span>
		</div>

		<div className='w-full bg-gray-200 rounded-full h-3'>
			<div
				className='bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out'
				style={{ width: `${uploadState.progress}%` }}></div>
		</div>

		{uploadState.success && (
			<div className='flex items-center gap-2 text-green-600'>
				<FaCheckCircle />
				<span className='text-sm font-medium'>
					Upload completed successfully!
				</span>
			</div>
		)}
	</div>
));

const ErrorAlert = memo(({ error, onDismiss }) => (
	<div className='bg-red-50 border border-red-200 rounded-lg p-4 mb-6'>
		<div className='flex items-center gap-2 text-red-800'>
			<FaExclamationTriangle />
			<span className='font-medium'>Upload Error</span>
		</div>
		<p className='text-red-700 mt-1'>{error}</p>
		<button
			onClick={onDismiss}
			className='text-red-600 underline text-sm mt-2 hover:text-red-800'>
			Dismiss
		</button>
	</div>
));

const FileUploadArea = memo(
	({
		type,
		accept,
		icon: Icon,
		label,
		file,
		preview,
		isDragging,
		isUploading,
		onFileSelect,
		onRemoveFile,
		onDragOver,
		onDragLeave,
		onDrop,
	}) => {
		const handleClick = useCallback(() => {
			if (!isUploading) {
				document.getElementById(type).click();
			}
		}, [type, isUploading]);

		const handleFileChange = useCallback(
			(e) => {
				const { files: selectedFiles } = e.target;
				if (selectedFiles && selectedFiles[0]) {
					onFileSelect(selectedFiles[0]);
				}
			},
			[onFileSelect],
		);

		const handleDragOver = useCallback(
			(e) => {
				e.preventDefault();
				if (!isUploading) {
					onDragOver();
				}
			},
			[isUploading, onDragOver],
		);

		const handleDragLeave = useCallback(
			(e) => {
				e.preventDefault();
				if (!isUploading) {
					onDragLeave();
				}
			},
			[isUploading, onDragLeave],
		);

		const handleDrop = useCallback(
			(e) => {
				e.preventDefault();
				if (!isUploading) {
					onDrop();
					const droppedFiles = e.dataTransfer.files;
					if (droppedFiles && droppedFiles[0]) {
						onFileSelect(droppedFiles[0]);
					}
				}
			},
			[isUploading, onDrop, onFileSelect],
		);

		return (
			<div className='space-y-2'>
				<label className='block text-sm font-medium text-gray-700 mb-2'>
					{label} *
				</label>

				{!file ? (
					<div
						className={`border-2 border-dashed rounded-lg p-6 text-center transition-all ${
							isDragging
								? 'border-blue-500 bg-blue-50'
								: 'border-gray-300 hover:border-gray-400'
						} ${
							isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
						}`}
						onDragOver={handleDragOver}
						onDragLeave={handleDragLeave}
						onDrop={handleDrop}
						onClick={handleClick}>
						<Icon className='mx-auto h-12 w-12 text-gray-400 mb-4' />
						<p className='text-sm text-gray-600 mb-2'>
							Drag and drop your {type} here, or{' '}
							<span className='text-blue-600 font-medium'>browse</span>
						</p>
						<p className='text-xs text-gray-500'>
							{type === 'thumbnail'
								? 'PNG, JPG, GIF up to 10MB'
								: 'MP4, MOV, AVI up to 500MB'}
						</p>
						<input
							type='file'
							id={type}
							name={type}
							accept={accept}
							onChange={handleFileChange}
							disabled={isUploading}
							className='hidden'
						/>
					</div>
				) : (
					<div className='border border-gray-300 rounded-lg p-4 bg-gray-50'>
						<div className='flex items-center justify-between mb-3'>
							<p className='text-sm font-medium text-gray-700'>Preview:</p>
							<button
								type='button'
								onClick={onRemoveFile}
								disabled={isUploading}
								className='text-red-500 hover:text-red-700 disabled:opacity-50'>
								<FaTimes />
							</button>
						</div>

						{type === 'thumbnail' && preview && (
							<div className='mb-3'>
								<img
									src={preview}
									alt='Thumbnail preview'
									className='max-w-full h-48 object-cover rounded-lg mx-auto'
								/>
							</div>
						)}

						{type === 'video' && preview && (
							<div className='mb-3'>
								<video
									src={preview}
									controls
									className='max-w-full h-48 rounded-lg mx-auto'
								/>
							</div>
						)}

						<p className='text-sm text-gray-600'>
							<span className='font-medium'>Selected:</span> {file.name}
						</p>
						<p className='text-xs text-gray-500'>
							Size: {(file.size / (1024 * 1024)).toFixed(2)} MB
						</p>
					</div>
				)}
			</div>
		);
	},
);

export default function LessonUploadForm() {
	const router = useRouter();
	const { data: categories } = useLessonCategories();
	const uploadLessonMutation = useUploadLesson();

	const [formData, setFormData] = useState({
		title: '',
		instructor: '',
		category: '',
		description: '',
	});

	const [files, setFiles] = useState({
		thumbnail: null,
		video: null,
	});

	const [previews, setPreviews] = useState({
		thumbnail: null,
		video: null,
	});

	const [dragStates, setDragStates] = useState({
		thumbnail: false,
		video: false,
	});

	const [uploadState, setUploadState] = useState({
		isUploading: false,
		progress: 0,
		currentStep: '',
		error: null,
		success: false,
	});

	// Memoized callbacks to prevent unnecessary re-renders
	const handleInputChange = useCallback((e) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));
	}, []);

	const createPreview = useCallback((file, type) => {
		if (type === 'thumbnail' && file.type.startsWith('image/')) {
			return URL.createObjectURL(file);
		} else if (type === 'video' && file.type.startsWith('video/')) {
			return URL.createObjectURL(file);
		}
		return null;
	}, []);

	const validateFileSize = useCallback((file, type) => {
		const MAX_VIDEO_SIZE = 500 * 1024 * 1024; // 500MB
		const MAX_THUMB_SIZE = 10 * 1024 * 1024; // 10MB

		if (type === 'video' && file.size > MAX_VIDEO_SIZE) {
			alert('Video file is too large. Maximum size is 500MB.');
			return false;
		}

		if (type === 'thumbnail' && file.size > MAX_THUMB_SIZE) {
			alert('Thumbnail file is too large. Maximum size is 10MB.');
			return false;
		}

		return true;
	}, []);

	const handleFileSelect = useCallback(
		(file, type) => {
			if (!file) return;

			// Validate file type
			if (type === 'thumbnail' && !file.type.startsWith('image/')) {
				alert('Please select an image file for thumbnail');
				return;
			}
			if (type === 'video' && !file.type.startsWith('video/')) {
				alert('Please select a video file');
				return;
			}

			// Validate file size
			if (!validateFileSize(file, type)) {
				return;
			}

			// Clean up previous preview
			setPreviews((prev) => {
				if (prev[type]) {
					URL.revokeObjectURL(prev[type]);
				}
				const preview = createPreview(file, type);
				return { ...prev, [type]: preview };
			});

			setFiles((prev) => ({
				...prev,
				[type]: file,
			}));
		},
		[validateFileSize, createPreview],
	);

	const removeFile = useCallback((type) => {
		setPreviews((prev) => {
			if (prev[type]) {
				URL.revokeObjectURL(prev[type]);
			}
			return { ...prev, [type]: null };
		});

		setFiles((prev) => ({ ...prev, [type]: null }));
	}, []);

	const handleDragOver = useCallback((type) => {
		setDragStates((prev) => ({ ...prev, [type]: true }));
	}, []);

	const handleDragLeave = useCallback((type) => {
		setDragStates((prev) => ({ ...prev, [type]: false }));
	}, []);

	const handleDrop = useCallback((type) => {
		setDragStates((prev) => ({ ...prev, [type]: false }));
	}, []);

	const dismissError = useCallback(() => {
		setUploadState((prev) => ({ ...prev, error: null }));
	}, []);

	const handleBack = useCallback(() => {
		if (!uploadState.isUploading) {
			router.back();
		}
	}, [router, uploadState.isUploading]);

	const handleSubmit = useCallback(
		async (e) => {
			e.preventDefault();

			// Validate form
			if (
				!formData.title.trim() ||
				!formData.instructor.trim() ||
				!formData.category ||
				!formData.description.trim()
			) {
				setUploadState((prev) => ({
					...prev,
					error: 'Please fill in all required fields.',
				}));
				return;
			}

			if (!files.thumbnail || !files.video) {
				setUploadState((prev) => ({
					...prev,
					error: 'Please select both thumbnail and video files.',
				}));
				return;
			}

			setUploadState({
				isUploading: true,
				progress: 0,
				currentStep: 'Starting upload...',
				error: null,
				success: false,
			});

			try {
				const formDataToSend = new FormData();

// Prepare lesson data
				const lessonData = {
					...formData,
					thumbnail: files.thumbnail,
					videoUrl: files.video
				};

				await uploadLessonMutation.mutateAsync(lessonData);

				setUploadState((prev) = 3e ({
					...prev,
					success: true,
					progress: 100,
					currentStep: 'Lesson uploaded and notifications sent!',
				}));

				// Redirect after success
				setTimeout(() => {
					router.push('/lessons');
				}, 2000);
			} catch (error) {
				console.error('Upload error:', error);

				let errorMessage = 'Failed to upload lesson. Please try again.';

				if (error?.response?.status === 408) {
					errorMessage =
						'Upload timeout. Please check your connection and try again.';
				} else if (error?.response?.data?.message) {
					errorMessage = error.response.data.message;
				}

				setUploadState((prev) => ({
					...prev,
					isUploading: false,
					error: errorMessage,
					progress: 0,
					currentStep: '',
				}));
			}
		},
		[formData, files, uploadLessonMutation, router],
	);

	// Memoized file upload handlers
	const thumbnailHandlers = useMemo(
		() => ({
			onFileSelect: (file) => handleFileSelect(file, 'thumbnail'),
			onRemoveFile: () => removeFile('thumbnail'),
			onDragOver: () => handleDragOver('thumbnail'),
			onDragLeave: () => handleDragLeave('thumbnail'),
			onDrop: () => handleDrop('thumbnail'),
		}),
		[handleFileSelect, removeFile, handleDragOver, handleDragLeave, handleDrop],
	);

	const videoHandlers = useMemo(
		() => ({
			onFileSelect: (file) => handleFileSelect(file, 'video'),
			onRemoveFile: () => removeFile('video'),
			onDragOver: () => handleDragOver('video'),
			onDragLeave: () => handleDragLeave('video'),
			onDrop: () => handleDrop('video'),
		}),
		[handleFileSelect, removeFile, handleDragOver, handleDragLeave, handleDrop],
	);

	// Memoized category options
	const categoryOptions = useMemo(
		() =>
			categories?.map((category) => (
				<option
					key={category}
					value={category}>
					{category}
				</option>
			)) || [],
		[categories],
	);

	return (
		<AdminLayout>
			<div className='min-h-screen bg-gray-50 py-6'>
				<div className='max-w-4xl mx-auto px-6'>
					<div className='flex items-center mb-6'>
						<button
							onClick={handleBack}
							disabled={uploadState.isUploading}
							className='flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'>
							<FaArrowLeft />
							<span className='text-gray-800'>Back</span>
						</button>
					</div>

					<div className='bg-white rounded-lg shadow-lg p-8'>
						<h1 className='text-3xl font-bold text-gray-900 mb-8'>
							Upload New Lesson
						</h1>

						{uploadState.error && (
							<ErrorAlert
								error={uploadState.error}
								onDismiss={dismissError}
							/>
						)}

						<form
							onSubmit={handleSubmit}
							className='space-y-6'>
							{/* Title */}
							<div>
								<label
									htmlFor='title'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Title *
								</label>
								<input
									type='text'
									id='title'
									name='title'
									value={formData.title}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
									placeholder='Enter lesson title'
								/>
							</div>

							{/* Instructor */}
							<div>
								<label
									htmlFor='instructor'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Instructor *
								</label>
								<input
									type='text'
									id='instructor'
									name='instructor'
									value={formData.instructor}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
									placeholder='Enter instructor name'
								/>
							</div>

							{/* Category */}
							<div>
								<label
									htmlFor='category'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Category *
								</label>
								<select
									id='category'
									name='category'
									value={formData.category}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'>
									<option value=''>Select a category</option>
									{categoryOptions}
								</select>
							</div>

							{/* Description */}
							<div>
								<label
									htmlFor='description'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Description *
								</label>
								<textarea
									id='description'
									name='description'
									value={formData.description}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									rows={4}
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed resize-none'
									placeholder='Enter lesson description'
								/>
							</div>

							{/* File Upload Areas */}
							<div className='grid md:grid-cols-2 gap-6'>
								<FileUploadArea
									type='thumbnail'
									accept='image/*'
									icon={FaImage}
									label='Thumbnail Image'
									file={files.thumbnail}
									preview={previews.thumbnail}
									isDragging={dragStates.thumbnail}
									isUploading={uploadState.isUploading}
									{...thumbnailHandlers}
								/>

								<FileUploadArea
									type='video'
									accept='video/*'
									icon={FaVideo}
									label='Video File'
									file={files.video}
									preview={previews.video}
									isDragging={dragStates.video}
									isUploading={uploadState.isUploading}
									{...videoHandlers}
								/>
							</div>

							{/* Submit Button */}
							<div className='pt-6'>
								<button
									type='submit'
									disabled={uploadState.isUploading || uploadState.success}
									className='w-full flex items-center cursor-pointer justify-center gap-3 bg-teal-600 hover:bg-teal-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100'>
									{uploadState.isUploading ? (
										<>
											<div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
											<span>Uploading...</span>
										</>
									) : uploadState.success ? (
										<>
											<FaCheckCircle className='text-lg' />
											<span>Upload Complete!</span>
										</>
									) : (
										<>
											<FaUpload className='text-lg' />
											<span>Upload Lesson</span>
										</>
									)}
								</button>

								{/* Progress Bar */}
								{uploadState.isUploading && (
									<div className='bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4'>
										<ProgressBar uploadState={uploadState} />
									</div>
								)}
							</div>
						</form>
					</div>
				</div>
			</div>
		</AdminLayout>
	);
}
