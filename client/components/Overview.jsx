import React from 'react';
import {
	Radar,
	RadarChart,
	PolarGrid,
	PolarAngleAxis,
	PolarRadiusAxis,
} from 'recharts';
import { useState } from 'react';
const Overview = () => {
	const [actions, setActions] = useState(['', '', '', '']);

	// Data for radar chart
	const radarData = [
		{ subject: 'Emotions', A: 80, fullMark: 100 },
		{ subject: 'Beliefs', A: 65, fullMark: 100 },
		{ subject: 'Mindset', A: 75, fullMark: 100 },
		{ subject: 'Perception', A: 60, fullMark: 100 },
		{ subject: 'Excuses', A: 70, fullMark: 100 },
	];
	// Handle action input changes
	const handleActionChange = (index, value) => {
		const newActions = [...actions];
		newActions[index] = value;
		setActions(newActions);
	};
	return (
		<div className=''>
			<div className='flex justify-center mb-6'>
				<div className='w-full'>
					{/* Radar Chart using Recharts */}
					<div className='w-full flex justify-center'>
						<RadarChart
							outerRadius={90}
							width={300}
							height={250}
							data={radarData}>
							<PolarGrid />
							<PolarAngleAxis dataKey='subject' />
							<PolarRadiusAxis
								angle={30}
								domain={[0, 100]}
							/>
							<Radar
								name='Current'
								dataKey='A'
								stroke='#8884d8'
								fill='#8884d8'
								fillOpacity={0.6}
							/>
						</RadarChart>
					</div>
				</div>
			</div>

			{/* ACTIONS Section */}
			<div className='mt-2 mb-4'>
				<h2 className='text-center font-bold text-lg mb-2'>ACTIONS</h2>
				<div className='border border-gray-300 rounded'>
					{[1, 2, 3, 4].map((num, index) => (
						<div
							key={`action-${num}`}
							className='flex border-b last:border-b-0 border-gray-300'>
							<div className='p-2 border-r border-gray-300 w-8 text-center'>
								{num}
							</div>
							<input
								type='text'
								className='flex-grow p-2 outline-none'
								value={actions[index]}
								onChange={(e) => handleActionChange(index, e.target.value)}
								placeholder={`Action ${num}`}
							/>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default Overview;
