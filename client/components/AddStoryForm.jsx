import { useState } from 'react';

const AddStoryForm = ({ onSubmit, onCancel }) => {
	const [title, setTitle] = useState('');
	const [detail, setDetail] = useState('');
	const [lesson, setLesson] = useState('');
	const [date, setDate] = useState('');

	const handleSubmit = (e) => {
		e.preventDefault();
		onSubmit({
			title,
			detail,
			lesson,
			date,
		});
	};

	return (
		<form onSubmit={handleSubmit}>
			<div className='mb-4'>
				<label className='block text-xl mb-2'>Story Title</label>
				<input
					type='text'
					value={title}
					onChange={(e) => setTitle(e.target.value)}
					className='w-full p-2 border rounded-lg bg-white shadow-md'
				/>
			</div>

			<div className='mb-4'>
				<label className='block text-xl mb-2'>Story Detail</label>
				<textarea
					value={detail}
					onChange={(e) => setDetail(e.target.value)}
					className='w-full p-2 border rounded-lg bg-white shadow-md'
					rows='4'
				/>
			</div>

			<div className='mb-4'>
				<label className='block text-xl mb-2'>Lesson Learnt</label>
				<textarea
					value={lesson}
					onChange={(e) => setLesson(e.target.value)}
					className='w-full p-2 border rounded-lg bg-white shadow-md'
					rows='2'
				/>
			</div>

			<div className='mb-6'>
				<label className='block text-xl mb-2'>Date</label>
				<input
					type='text'
					value={date}
					onChange={(e) => setDate(e.target.value)}
					className='w-full p-2 border rounded-lg bg-white shadow-md'
					placeholder='DD/MM/YYYY'
				/>
			</div>

			<div className='flex justify-between'>
				<button
					type='button'
					onClick={onCancel}
					className='px-6 py-3 bg-white rounded-lg text-gray-800 font-bold text-lg'>
					Discard
				</button>
				<button
					type='submit'
					className='px-6 py-3 bg-gray-900 text-white rounded-lg font-bold text-lg'>
					Save
				</button>
			</div>
		</form>
	);
};

export default AddStoryForm;
