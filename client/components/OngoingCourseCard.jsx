import React from 'react';
import { FaP<PERSON>, FaBook, FaClock } from 'react-icons/fa';

const OngoingCourseCard = ({
	title,
	progress,
	completedLessons,
	totalLessons,
}) => {
	const getProgressColor = (progress) => {
		if (progress < 30) return 'from-red-400 to-red-500';
		if (progress < 70) return 'from-yellow-400 to-orange-500';
		return 'from-green-400 to-green-500';
	};

	const getProgressBg = (progress) => {
		if (progress < 30) return 'bg-red-50 dark:bg-red-900/20';
		if (progress < 70) return 'bg-yellow-50 dark:bg-yellow-900/20';
		return 'bg-green-50 dark:bg-green-900/20';
	};

	return (
		<div className='bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl border border-gray-100 dark:border-gray-700 w-full min-h-[280px] flex flex-col'>
			{/* Header */}
			<div className='flex items-start justify-between mb-4'>
				<div className='flex-1 min-w-0'>
					<h3 className='text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-2 leading-tight'>
						{title}
					</h3>
					<div className='flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400'>
						<FaBook className='w-3 h-3' />
						<span>{totalLessons} lessons</span>
					</div>
				</div>
				<div className={`p-2 rounded-lg ${getProgressBg(progress)}`}>
					<FaClock className='w-4 h-4 text-gray-600 dark:text-gray-400' />
				</div>
			</div>

			{/* Progress Section */}
			<div className='flex-1 flex flex-col justify-center mb-6'>
				<div className='flex items-center justify-between mb-3'>
					<span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
						Progress
					</span>
					<span className='text-sm font-bold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full'>
						{progress}%
					</span>
				</div>

				{/* Progress Bar */}
				<div className='relative w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-3 overflow-hidden'>
					<div
						className={`absolute top-0 left-0 h-full bg-gradient-to-r ${getProgressColor(
							progress,
						)} rounded-full transition-all duration-700 ease-out`}
						style={{ width: `${progress}%` }}
					/>
					<div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full animate-pulse' />
				</div>

				{/* Lesson Counter */}
				<div className='flex items-center justify-between'>
					<span className='text-sm text-gray-600 dark:text-gray-400'>
						<span className='font-semibold text-gray-900 dark:text-white'>
							{completedLessons}
						</span>{' '}
						of {totalLessons} lessons completed
					</span>
					<div className='flex items-center gap-1'>
						{[...Array(5)].map((_, i) => (
							<div
								key={i}
								className={`w-1.5 h-1.5 rounded-full ${
									i < Math.ceil((progress / 100) * 5)
										? 'bg-gradient-to-r ' + getProgressColor(progress)
										: 'bg-gray-300 dark:bg-gray-600'
								}`}
							/>
						))}
					</div>
				</div>
			</div>

			{/* Action Button */}
			<button className='w-full bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center gap-2 group'>
				<FaPlay className='w-4 h-4 group-hover:translate-x-1 transition-transform duration-200' />
				<span>Continue Learning</span>
			</button>

			{/* Decorative Elements */}
			<div className='absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-teal-100/20 to-transparent dark:from-teal-900/20 rounded-full -translate-y-10 translate-x-10 pointer-events-none' />
			<div className='absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-blue-100/20 to-transparent dark:from-blue-900/20 rounded-full translate-y-8 -translate-x-8 pointer-events-none' />
		</div>
	);
};

export default OngoingCourseCard;
