{"name": "you-life", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "@videojs/themes": "^1.0.1", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.9.2", "loadash": "^1.0.0", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "lucide-react": "^0.522.0", "next": "15.3.1", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-player": "^2.16.0", "recharts": "^2.15.3", "shadcn": "^2.7.0", "swiper": "^11.2.6", "tailwind-merge": "^3.3.1", "video.js": "^8.23.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4"}}