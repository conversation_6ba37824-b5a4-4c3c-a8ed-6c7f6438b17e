'use client';
import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import FixedHeader from '@/src/components/FixedHeader';
import Sidebar from '@/src/components/Sidebar';
import AddTaskModal from '@/src/components/AddTaskModal';
import { goalsAPI } from '@/services/api';
import toast from 'react-hot-toast';

export default function GoalDetail({ params }) {
	const router = useRouter();
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [selectedGoal, setSelectedGoal] = useState(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [showAddTaskModal, setShowAddTaskModal] = useState(false);
	const [currentTaskCategory, setCurrentTaskCategory] = useState(null);
	const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [showDeleteTaskConfirm, setShowDeleteTaskConfirm] = useState(false);
	const [taskToDelete, setTaskToDelete] = useState(null);
	const [isDeletingTask, setIsDeletingTask] = useState(false);

	// Unwrap the params object using React.use()
	const unwrappedParams = use(params);

	useEffect(() => {
		const loadGoal = async () => {
			try {
				setLoading(true);
				const response = await goalsService.getGoalById(unwrappedParams.id);
				setSelectedGoal(response.data.goal);
				setError(null);
			} catch (err) {
				console.error('Failed to load goal:', err);
				setError('Failed to load goal. Please try again.');
			} finally {
				setLoading(false);
			}
		};

		if (unwrappedParams.id) {
			loadGoal();
		}
	}, [unwrappedParams.id]);

	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

	const handleBackToGoals = () => {
		router.push('/goals');
	};

	const handleDeleteGoal = async () => {
		if (!selectedGoal || isDeleting) return;

		try {
			setIsDeleting(true);
			await goalsService.deleteGoal(selectedGoal._id);
			// Redirect to goals page after successful deletion
			router.push('/goals');
		} catch (error) {
			console.error('Failed to delete goal:', error);
			setError('Failed to delete goal. Please try again.');
			setIsDeleting(false);
		}
		setShowDeleteConfirm(false);
	};

	const handleDeleteTask = async () => {
		if (!selectedGoal || !taskToDelete || isDeletingTask) return;

		try {
			setIsDeletingTask(true);
			const response = await goalsService.deleteTask(
				selectedGoal._id,
				taskToDelete._id,
			);
			setSelectedGoal(response.data.goal);
			setShowDeleteTaskConfirm(false);
			setTaskToDelete(null);
		} catch (error) {
			console.error('Failed to delete task:', error);
			setError('Failed to delete task. Please try again.');
		} finally {
			setIsDeletingTask(false);
		}
	};

	const handleTaskToggle = async (taskId) => {
		if (!selectedGoal) return;

		const taskIndex = selectedGoal.tasks.findIndex((t) => t._id === taskId);
		if (taskIndex === -1) return;

		const task = selectedGoal.tasks[taskIndex];
		const originalStatus = task.status;
		const newStatus =
			originalStatus === 'Completed' ? 'Not Started' : 'Completed';

		const updatedTasks = [...selectedGoal.tasks];
		updatedTasks[taskIndex] = { ...task, status: newStatus };
		setSelectedGoal({ ...selectedGoal, tasks: updatedTasks });

		try {
			const response = await goalsService.updateTaskStatus(
				selectedGoal._id,
				taskId,
				newStatus,
			);
			setSelectedGoal(response.data.goal);
		} catch (error) {
			updatedTasks[taskIndex] = { ...task, status: originalStatus };
			setSelectedGoal({ ...selectedGoal, tasks: updatedTasks });

			console.error('Failed to update task status:', error);

			if (typeof toast === 'function') {
				toast.error('Could not complete request. Please try again.');
			} else {
				alert('Could not complete request. Please try again.');
			}
		}
	};

	const handleAddTask = (taskType) => {
		if (!selectedGoal) return;

		setCurrentTaskCategory(taskType);
		setShowAddTaskModal(true);
	};

	const handleSaveTask = async ({ category, text }) => {
		if (!selectedGoal || !text) return;

		try {
			const response = await goalsService.addTask(selectedGoal._id, {
				name: text,
				status: 'Not Started',
				category,
			});
			setSelectedGoal(response.data.goal);
			setShowAddTaskModal(false);
		} catch (error) {
			console.error('Failed to add task:', error);
			setError('Failed to add task. Please try again.');
		}
	};

	const handleDiscardTask = () => {
		setShowAddTaskModal(false);
	};

	const handleDeleteTaskClick = (task) => {
		setTaskToDelete(task);
		setShowDeleteTaskConfirm(true);
	};

	// Calculate progress
	const completedTasks =
		selectedGoal?.tasks?.filter((task) => task.status === 'Completed').length ||
		0;
	const totalTasks = selectedGoal?.tasks?.length || 0;
	const progress =
		totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
	const isCompleted = totalTasks > 0 && completedTasks === totalTasks;

	// Group tasks by category
	const groupedTasks =
		selectedGoal?.tasks?.reduce((acc, task) => {
			const category = task.category || 'amber'; // Default to amber if no category
			if (!acc[category]) {
				acc[category] = [];
			}
			acc[category].push(task);
			return acc;
		}, {}) || {};

	// Category configurations
	const categoryConfig = {
		red: {
			title: 'Red Tasks',
			bgColor: 'bg-red-50 dark:bg-red-900/20',
			borderColor: 'border-red-200 dark:border-red-800',
			headerColor: 'text-red-700 dark:text-red-300',
			iconColor: 'bg-red-500',
			description: '(Start here to set yourself up for success)',
		},
		amber: {
			title: 'Amber Tasks',
			bgColor: 'bg-amber-50 dark:bg-amber-900/20',
			borderColor: 'border-amber-200 dark:border-amber-800',
			headerColor: 'text-amber-700 dark:text-amber-300',
			iconColor: 'bg-amber-500',
			description: '(Stay on track with these tasks)',
		},
		green: {
			title: 'Completed Tasks',
			bgColor: 'bg-green-50 dark:bg-green-900/20',
			borderColor: 'border-green-200 dark:border-green-800',
			headerColor: 'text-green-700 dark:text-green-300',
			iconColor: 'bg-green-500',
			description: '(Tasks automatically move here when completed)',
		},
	};

	const renderTaskCategory = (category, tasks) => {
		const config = categoryConfig[category];
		if (!config) return null;

		// For green section, only show if there are completed tasks
		// For red and amber sections, show even if empty so users can add tasks
		if (category === 'green' && (!tasks || tasks.length === 0)) return null;
		if ((category === 'red' || category === 'amber') && (!tasks || tasks.length === 0)) {
			// Show empty state for red and amber to allow adding tasks
			return (
				<div
					key={category}
					className={`${config.bgColor} ${config.borderColor} border rounded-3xl p-6 mb-6`}>
					<div className='flex items-center justify-between mb-4'>
						<div className='flex items-center gap-3'>
							<div className={`w-3 h-3 ${config.iconColor} rounded-full`}></div>
							<h3 className={`text-lg font-bold ${config.headerColor}`}>
								{config.title}
							</h3>
							<span
								className={`text-sm px-2 py-1 rounded-full ${config.bgColor} ${config.headerColor}`}>
								0
							</span>
						</div>
						<button
							onClick={() => handleAddTask(category)}
							className={`${config.iconColor} hover:opacity-80 text-white p-2 rounded-xl transition-all duration-200`}>
							<svg
								className='w-4 h-4'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M12 4v16m8-8H4'
								/>
							</svg>
						</button>
					</div>
					<p className={`text-sm ${config.headerColor} mb-4 opacity-80`}>
						{config.description}
					</p>
					<div className='text-center py-8'>
						<p className={`text-sm ${config.headerColor} opacity-60`}>
							No tasks yet. Click the + button to add your first task.
						</p>
					</div>
				</div>
			);
		}

		return (
			<div
				key={category}
				className={`${config.bgColor} ${config.borderColor} border rounded-3xl p-6 mb-6`}>
				<div className='flex items-center justify-between mb-4'>
					<div className='flex items-center gap-3'>
						<div className={`w-3 h-3 ${config.iconColor} rounded-full`}></div>
						<h3 className={`text-lg font-bold ${config.headerColor}`}>
							{config.title}
						</h3>
						<span
							className={`text-sm px-2 py-1 rounded-full ${config.bgColor} ${config.headerColor}`}>
							{tasks.length}
						</span>
					</div>
					{/* Only show add button for red and amber sections */}
					{category !== 'green' && (
						<button
							onClick={() => handleAddTask(category)}
							className={`${config.iconColor} hover:opacity-80 text-white p-2 rounded-xl transition-all duration-200`}>
							<svg
								className='w-4 h-4'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M12 4v16m8-8H4'
								/>
							</svg>
						</button>
					)}
				</div>
				<p className={`text-sm ${config.headerColor} mb-4 opacity-80`}>
					{config.description}
				</p>
				<div className='space-y-3'>
					{tasks.map((task, index) => (
						<div
							key={task._id}
							className={`group relative bg-white dark:bg-slate-800 rounded-2xl p-4 hover:shadow-md transition-all duration-200 ${
								task.status === 'Completed'
									? 'bg-green-50 dark:bg-green-900/20'
									: ''
							}`}>
							<div className='flex items-center gap-4'>
								{/* Task Number */}
								<div
									className={`flex-shrink-0 w-8 h-8 ${config.iconColor} text-white rounded-full md:flex items-center justify-center text-sm font-semibold hidden`}>
									{index + 1}
								</div>

								{/* Task Content */}
								<div className='flex-1'>
									<p
										className={`text-gray-900 dark:text-white font-medium ${
											task.status === 'Completed'
												? 'line-through text-gray-500 dark:text-gray-400'
												: ''
										}`}>
										{task.name}
									</p>
									<div className='flex items-center gap-2 mt-1'>
										<span
											className={`text-xs px-2 py-1 rounded-full font-medium ${
												task.status === 'Completed'
													? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200'
													: 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
											}`}>
											{task.status}
										</span>
									</div>
								</div>

								{/* Task Actions */}
								<div className='flex items-center gap-2'>
									{/* Delete Task Button */}
									<button
										onClick={() => handleDeleteTaskClick(task)}
										className='flex-shrink-0 w-8 h-8 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100'>
										<svg
											className='w-4 h-4'
											fill='none'
											stroke='currentColor'
											viewBox='0 0 24 24'>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={2}
												d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
											/>
										</svg>
									</button>

									{/* Task Toggle Button */}
									<button
										onClick={() => handleTaskToggle(task._id)}
										className={`flex-shrink-0 w-8 h-8 border-2 rounded-full flex items-center justify-center transition-all duration-200 ${
											task.status === 'Completed'
												? 'bg-green-500 border-green-500 text-white hover:bg-green-600'
												: 'border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400'
										}`}>
										{task.status === 'Completed' && (
											<svg
												className='w-4 h-4'
												fill='none'
												stroke='currentColor'
												viewBox='0 0 24 24'>
												<path
													strokeLinecap='round'
													strokeLinejoin='round'
													strokeWidth={2}
													d='M5 13l4 4L19 7'
												/>
											</svg>
										)}
									</button>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		);
	};

	return (
		<div className='flex flex-col min-h-screen '>
			<FixedHeader
				toggleSidebar={toggleSidebar}
				title={'Goal Details'}
				showBackButton={true}
				onBackClick={handleBackToGoals}
			/>

			<div className='flex-1 px-4 sm:px-6 lg:px-8 py-6 max-w-4xl mx-auto w-full'>
				{/* Loading State */}
				{loading && (
					<div className='text-center py-16'>
						<div className='inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4'></div>
						<p className='text-lg text-gray-600 dark:text-gray-300'>
							Loading goal details...
						</p>
					</div>
				)}

				{/* Error State */}
				{error && (
					<div className='text-center py-16'>
						<div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl p-8 max-w-md mx-auto'>
							<div className='text-red-500 mb-4'>
								<svg
									className='w-12 h-12 mx-auto'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
									/>
								</svg>
							</div>
							<p className='text-red-700 dark:text-red-300 text-lg mb-4'>
								{error}
							</p>
							<button
								onClick={() => window.location.reload()}
								className='bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-xl transition-colors'>
								Try Again
							</button>
						</div>
					</div>
				)}

				{/* Goal Content */}
				{!loading && !error && selectedGoal && (
					<div className='space-y-8'>
						{/* Goal Header */}
						<div className='text-center'>
							<div className='relative inline-block'>
								<h1
									className={`text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 ${
										isCompleted
											? 'line-through text-gray-500 dark:text-gray-400'
											: ''
									}`}>
									{selectedGoal.title}
								</h1>
								{isCompleted && (
									<div className='absolute -top-2 -right-8 bg-gradient-to-r from-yellow-400 to-orange-500 text-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg'>
										<svg
											className='w-5 h-5'
											fill='currentColor'
											viewBox='0 0 24 24'>
											<path d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' />
										</svg>
									</div>
								)}
							</div>
						</div>

						{/* Progress Overview */}
						<div className='bg-white dark:bg-slate-800 rounded-3xl p-6 shadow-lg border border-gray-100 dark:border-slate-700'>
							<div className='flex items-center justify-between mb-4'>
								<h2 className='text-xl font-bold text-gray-900 dark:text-white'>
									Progress Overview
								</h2>
								<div className='text-right'>
									<div className='text-3xl font-bold text-gray-900 dark:text-white'>
										{progress}%
									</div>
									<div className='text-sm text-gray-600 dark:text-gray-400'>
										Complete
									</div>
								</div>
							</div>

							{/* Progress Bar */}
							<div className='mb-6'>
								<div className='w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 overflow-hidden'>
									<div
										className='h-full bg-gradient-to-r from-teal-300 to-teal-600 rounded-full transition-all duration-500 ease-out'
										style={{ width: `${progress}%` }}
									/>
								</div>
							</div>

							{/* Stats Grid */}
							<div className='grid grid-cols-1 sm:grid-cols-3 gap-4'>
								<div className='bg-blue-50 dark:bg-blue-900/20 rounded-2xl p-4 text-center'>
									<div className='text-2xl font-bold text-blue-600 dark:text-blue-400'>
										{totalTasks}
									</div>
									<div className='text-sm text-blue-600 dark:text-blue-400'>
										Total Tasks
									</div>
								</div>
								<div className='bg-green-50 dark:bg-green-900/20 rounded-2xl p-4 text-center'>
									<div className='text-2xl font-bold text-green-600 dark:text-green-400'>
										{completedTasks}
									</div>
									<div className='text-sm text-green-600 dark:text-green-400'>
										Completed
									</div>
								</div>
								<div className='bg-gray-50 dark:bg-gray-700 rounded-2xl p-4 text-center'>
									<div className='text-2xl font-bold text-gray-600 dark:text-gray-400'>
										{totalTasks - completedTasks}
									</div>
									<div className='text-sm text-gray-600 dark:text-gray-400'>
										Remaining
									</div>
								</div>
							</div>

							{isCompleted && (
								<div className='mt-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-4 border border-green-200 dark:border-green-800'>
									<div className='flex items-center justify-center gap-2 text-green-700 dark:text-green-300'>
										<svg
											className='w-6 h-6'
											fill='currentColor'
											viewBox='0 0 24 24'>
											<path d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' />
										</svg>
										<span className='font-semibold text-lg'>
											Congratulations! Goal Completed! 🎉
										</span>
									</div>
								</div>
							)}
						</div>

						{/* Tasks Section */}
						<div className='bg-white dark:bg-slate-800 rounded-3xl p-6 shadow-lg border border-gray-100 dark:border-slate-700'>
							<div className='flex items-center justify-between mb-6'>
								<h2 className='text-xl font-bold text-gray-900 dark:text-white'>
									Tasks
								</h2>
								<div className='flex items-center gap-2'>
									<button
										onClick={() => handleAddTask('red')}
										className='bg-teal-500 hover:bg-teal-600 text-white px-3 py-2 rounded-xl text-sm font-semibold transition-all duration-200 flex items-center gap-2'>
										<svg
											className='w-3 h-3'
											fill='none'
											stroke='currentColor'
											viewBox='0 0 24 24'>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={2}
												d='M12 4v16m8-8H4'
											/>
										</svg>
										Add Task
									</button>
								</div>
							</div>

							{/* Tasks by Category */}
							{selectedGoal.tasks && selectedGoal.tasks.length > 0 ? (
								<div className='space-y-0'>
									{['red', 'amber', 'green'].map((category) =>
										renderTaskCategory(category, groupedTasks[category]),
									)}
								</div>
							) : (
								<div className='text-center py-12'>
									<div className='text-gray-400 dark:text-gray-500 mb-4'>
										<svg
											className='w-16 h-16 mx-auto'
											fill='none'
											stroke='currentColor'
											viewBox='0 0 24 24'>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={1}
												d='M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z'
											/>
										</svg>
									</div>
									<h3 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
										No tasks yet
									</h3>
									<p className='text-gray-600 dark:text-gray-400 mb-4'>
										Break down your goal into actionable tasks to track your
										progress.
									</p>
									<div className='flex justify-center gap-3'>
										<button
											onClick={() => handleAddTask('red')}
											className='bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-xl font-semibold transition-colors'>
											Add Red Task
										</button>
										<button
											onClick={() => handleAddTask('amber')}
											className='bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-xl font-semibold transition-colors'>
											Add Amber Task
										</button>
									</div>
								</div>
							)}
						</div>

						{/* Quick Actions */}
						<div className='bg-white dark:bg-slate-800 rounded-3xl p-6 shadow-lg border border-gray-100 dark:border-slate-700'>
							<h2 className='text-xl font-bold text-gray-900 dark:text-white mb-4'>
								Quick Actions
							</h2>
							<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
								<button
									onClick={() => handleAddTask('amber')}
									className='flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-2xl hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors text-left'>
									<div className='w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center'>
										<svg
											className='w-5 h-5 text-white'
											fill='none'
											stroke='currentColor'
											viewBox='0 0 24 24'>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={2}
												d='M12 4v16m8-8H4'
											/>
										</svg>
									</div>
									<div>
										<div className='font-semibold text-blue-700 dark:text-blue-300'>
											Add Task
										</div>
										<div className='text-sm text-blue-600 dark:text-blue-400'>
											Create a new task for this goal
										</div>
									</div>
								</button>

								<button
									onClick={handleBackToGoals}
									className='flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-2xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors text-left'>
									<div className='w-10 h-10 bg-gray-500 rounded-full flex items-center justify-center'>
										<svg
											className='w-5 h-5 text-white'
											fill='none'
											stroke='currentColor'
											viewBox='0 0 24 24'>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={2}
												d='M9 5l7 7-7 7'
											/>
										</svg>
									</div>
									<div>
										<div className='font-semibold text-gray-700 dark:text-gray-300'>
											View All Goals
										</div>
										<div className='text-sm text-gray-600 dark:text-gray-400'>
											Go back to goals overview
										</div>
									</div>
								</button>

								<button
									onClick={() => setShowDeleteConfirm(true)}
									disabled={isDeleting}
									className='flex items-center gap-3 p-4 bg-red-50 dark:bg-red-900/20 rounded-2xl hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors text-left disabled:opacity-50 disabled:cursor-not-allowed'>
									<div className='w-10 h-10 bg-red-500 rounded-full flex items-center justify-center'>
										<svg
											className='w-5 h-5 text-white'
											fill='none'
											stroke='currentColor'
											viewBox='0 0 24 24'>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={2}
												d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
											/>
										</svg>
									</div>
									<div>
										<div className='font-semibold text-red-700 dark:text-red-300'>
											{isDeleting ? 'Deleting...' : 'Delete Goal'}
										</div>
										<div className='text-sm text-red-600 dark:text-red-400'>
											Permanently remove this goal
										</div>
									</div>
								</button>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Delete Goal Confirmation Modal */}
			{showDeleteConfirm && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
					<div className='bg-white dark:bg-slate-800 rounded-3xl p-6 max-w-md w-full mx-4'>
						<div className='text-center'>
							<div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 mb-4'>
								<svg
									className='h-6 w-6 text-red-600 dark:text-red-400'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M6 18L18 6M6 6l12 12'
									/>
								</svg>
							</div>
							<h3 className='text-lg font-semibold text-gray-900 dark:text-white'>
								Delete Goal?
							</h3>
							<p className='text-gray-600 dark:text-gray-400 mt-2'>
								Are you sure you want to permanently delete this goal and all
								its tasks?
							</p>
						</div>
						<div className='mt-6 flex justify-end gap-3'>
							<button
								onClick={() => setShowDeleteConfirm(false)}
								className='px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-xl text-gray-800 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 transition-all'>
								Cancel
							</button>
							<button
								onClick={handleDeleteGoal}
								className='px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all'>
								Delete
							</button>
						</div>
					</div>
				</div>
			)}

			{/* Delete Task Confirmation Modal */}
			{showDeleteTaskConfirm && taskToDelete && (
				<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
					<div className='bg-white dark:bg-slate-800 rounded-3xl p-6 max-w-md w-full mx-4'>
						<div className='text-center'>
							<div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 mb-4'>
								<svg
									className='h-6 w-6 text-red-600 dark:text-red-400'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M6 18L18 6M6 6l12 12'
									/>
								</svg>
							</div>
							<h3 className='text-lg font-semibold text-gray-900 dark:text-white'>
								Delete Task?
							</h3>
							<p className='text-gray-600 dark:text-gray-400 mt-2'>
								Are you sure you want to permanently delete the task "
								<strong>{taskToDelete.name}</strong>"?
							</p>
						</div>
						<div className='mt-6 flex justify-end gap-3'>
							<button
								onClick={() => {
									setShowDeleteTaskConfirm(false);
									setTaskToDelete(null);
								}}
								className='px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-xl text-gray-800 dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600 transition-all'>
								Cancel
							</button>
							<button
								onClick={handleDeleteTask}
								className='px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all disabled:opacity-50'
								disabled={isDeletingTask}>
								{isDeletingTask ? 'Deleting...' : 'Delete'}
							</button>
						</div>
					</div>
				</div>
			)}

			{/* Add Task Modal */}
			{showAddTaskModal && (
				<AddTaskModal
					category={currentTaskCategory}
					onSave={handleSaveTask}
					onDiscard={handleDiscardTask}
				/>
			)}

			{/* Sidebar (if you need to render it here) */}
			<Sidebar
				isOpen={isSidebarOpen}
				onClose={toggleSidebar}
			/>
		</div>
	);
}
