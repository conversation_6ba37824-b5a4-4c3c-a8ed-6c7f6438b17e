'use client';
import { AddGoalModal } from '@/src/components/features/goals/AddGoalModal';
import { FixedHeader } from '@/src/components/layout/FixedHeader';
import { Sidebar } from '@/src/components/layout/Sidebar';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { goalsService } from '@/src/lib/api';

const Goals = () => {
	const router = useRouter();
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [showAddForm, setShowAddForm] = useState(false);
	const [goals, setGoals] = useState([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [newGoal, setNewGoal] = useState({ title: '', description: '' });

	// Load goals from API
	useEffect(() => {
		const loadGoals = async () => {
			try {
				setLoading(true);
				const response = await goalsService.getUserGoals();
				setGoals(response.data.goals);
				setError(null);
			} catch (err) {
				console.error('Failed to load goals:', err);
				setError('Failed to load goals. Please try again.');
			} finally {
				setLoading(false);
			}
		};

		loadGoals();
	}, []);

	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
	const handleAddGoalClick = () => setShowAddForm(true);

	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setNewGoal((prev) => ({ ...prev, [name]: value }));
	};

	const handleSaveGoal = async () => {
		if (!newGoal.title) return;

		try {
			const goalData = {
				title: newGoal.title,
				tasks: [],
				isPublic: false,
			};

			const response = await goalsService.createGoal(goalData);
			setGoals([response.data.goal, ...goals]);
			setNewGoal({ title: '', description: '' });
			setShowAddForm(false);
		} catch (error) {
			console.error('Failed to create goal:', error);
			setError('Failed to create goal. Please try again.');
		}
	};

	const handleDiscard = () => {
		setNewGoal({ title: '', description: '' });
		setShowAddForm(false);
	};

	const handleGoalClick = (goal) => {
		router.push(`/goals/${goal.id}`);
	};

	return (
		<div className='flex flex-col min-h-screen '>
			<FixedHeader
				toggleSidebar={toggleSidebar}
				title={'Goals'}
				showBackButton={false}
			/>

			<div className='flex-1 px-4 sm:px-6 lg:px-8 py-6 max-w-7xl mx-auto w-full'>
				{/* Header Section */}
				<div className='text-center mb-8 sm:mb-12'>
					<h1 className='text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-3'>
						Your Goals
					</h1>
					<p className='text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto'>
						Track your progress and achieve your dreams, one goal at a time.
					</p>
				</div>

				{/* Add Goal Button */}
				<div className='flex justify-center mb-8'>
					<button
						onClick={handleAddGoalClick}
						className='group relative bg-gradient-to-r from-teal-600 to-teal-800 hover:from-teal-800 hover:to-teal-600 text-white font-semibold py-3 px-6 sm:py-4 sm:px-8 rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200 text-base sm:text-lg'>
						<span className='flex items-center gap-2'>
							<svg
								className='w-5 h-5'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M12 4v16m8-8H4'
								/>
							</svg>
							Add New Goal
						</span>
					</button>
				</div>

				{/* Loading State */}
				{loading && (
					<div className='text-center py-16'>
						<div className='inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4'></div>
						<p className='text-lg text-gray-600 dark:text-gray-300'>
							Loading your goals...
						</p>
					</div>
				)}

				{/* Error State */}
				{error && (
					<div className='text-center py-16'>
						<div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl p-8 max-w-md mx-auto'>
							<div className='text-red-500 mb-4'>
								<svg
									className='w-12 h-12 mx-auto'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
									/>
								</svg>
							</div>
							<p className='text-red-700 dark:text-red-300 text-lg mb-4'>
								{error}
							</p>
							<button
								onClick={() => window.location.reload()}
								className='bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-xl transition-colors'>
								Try Again
							</button>
						</div>
					</div>
				)}

				{/* Empty State */}
				{!loading && !error && goals.length === 0 && (
					<div className='text-center py-16'>
						<div className='bg-white dark:bg-slate-800 rounded-3xl p-8 sm:p-12 shadow-lg border border-gray-100 dark:border-slate-700 max-w-md mx-auto'>
							<div className='text-gray-400 dark:text-gray-500 mb-6'>
								<svg
									className='w-20 h-20 mx-auto'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={1}
										d='M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z'
									/>
								</svg>
							</div>
							<h3 className='text-2xl font-bold text-gray-900 dark:text-white mb-3'>
								No goals yet
							</h3>
							<p className='text-gray-600 dark:text-gray-400 mb-6'>
								Start your journey by creating your first goal. Every great
								achievement begins with a single step.
							</p>
							<button
								onClick={handleAddGoalClick}
								className='bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors'>
								Create Your First Goal
							</button>
						</div>
					</div>
				)}

				{/* Goals Grid */}
				{!loading && !error && goals.length > 0 && (
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8'>
						{goals.map((goal) => {
							const completedTasks = goal.tasks.filter(
								(task) => task.status === 'Completed',
							).length;
							const totalTasks = goal.tasks.length;
							const progress =
								totalTasks > 0
									? Math.round((completedTasks / totalTasks) * 100)
									: 0;
							const isCompleted =
								totalTasks > 0 && completedTasks === totalTasks;

							return (
								<div
									key={goal._id}
									className='group relative bg-white dark:bg-slate-800 rounded-3xl p-6 shadow-lg hover:shadow-xl border border-gray-100 dark:border-slate-700 cursor-pointer transform hover:-translate-y-2 transition-all duration-300'
									onClick={() => handleGoalClick({ id: goal._id, ...goal })}>
									{/* Completed Badge */}
									{isCompleted && (
										<div className='absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg'>
											<svg
												className='w-6 h-6'
												fill='currentColor'
												viewBox='0 0 24 24'>
												<path d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' />
											</svg>
										</div>
									)}

									{/* Goal Header */}
									<div className='mb-4'>
										<h3
											className={`text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-2 ${
												isCompleted
													? 'line-through text-gray-500 dark:text-gray-400'
													: ''
											}`}>
											{goal.title}
										</h3>

										<div className='flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400'>
											<svg
												className='w-4 h-4'
												fill='none'
												stroke='currentColor'
												viewBox='0 0 24 24'>
												<path
													strokeLinecap='round'
													strokeLinejoin='round'
													strokeWidth={2}
													d='M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z'
												/>
											</svg>
											{totalTasks === 0
												? 'No tasks yet'
												: `${totalTasks} tasks • ${completedTasks} completed`}
										</div>
									</div>

									{/* Progress Section */}
									{isCompleted ? (
										<div className='bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-4 border border-green-200 dark:border-green-800'>
											<div className='flex items-center justify-center gap-2 text-green-700 dark:text-green-300'>
												<svg
													className='w-5 h-5'
													fill='currentColor'
													viewBox='0 0 24 24'>
													<path d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z' />
												</svg>
												<span className='font-semibold'>Goal Completed!</span>
											</div>
										</div>
									) : (
										<div className='space-y-3'>
											{/* Progress Bar */}
											<div className='relative'>
												<div className='flex items-center justify-between mb-2'>
													<span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
														Progress
													</span>
													<span className='text-sm font-bold text-gray-900 dark:text-white'>
														{progress}%
													</span>
												</div>
												<div className='w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden'>
													<div
														className='h-full bg-gradient-to-r from-teal-300 to-teal-600 rounded-full transition-all duration-500 ease-out'
														style={{ width: `${progress}%` }}
													/>
												</div>
											</div>

											{/* Quick Stats */}
											{totalTasks > 0 && (
												<div className='grid grid-cols-2 gap-3 text-center'>
													<div className='bg-teal-50 dark:bg-teal-900/20 rounded-xl p-3'>
														<div className='text-lg font-bold text-teal-600 dark:text-teal-400'>
															{completedTasks}
														</div>
														<div className='text-xs text-teal-600 dark:text-teal-400'>
															Completed
														</div>
													</div>
													<div className='bg-gray-50 dark:bg-gray-700 rounded-xl p-3'>
														<div className='text-lg font-bold text-gray-600 dark:text-gray-400'>
															{totalTasks - completedTasks}
														</div>
														<div className='text-xs text-gray-600 dark:text-gray-400'>
															Remaining
														</div>
													</div>
												</div>
											)}
										</div>
									)}

									{/* Hover Effect Indicator */}
									<div className='absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200'>
										<div className='bg-teal-600 text-white p-2 rounded-full shadow-lg'>
											<svg
												className='w-4 h-4'
												fill='none'
												stroke='currentColor'
												viewBox='0 0 24 24'>
												<path
													strokeLinecap='round'
													strokeLinejoin='round'
													strokeWidth={2}
													d='M9 5l7 7-7 7'
												/>
											</svg>
										</div>
									</div>
								</div>
							);
						})}
					</div>
				)}
			</div>

			{showAddForm && (
				<AddGoalModal
					newGoal={newGoal}
					onChange={handleInputChange}
					onSave={handleSaveGoal}
					onDiscard={handleDiscard}
				/>
			)}

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Goals;
