'use client';
import FixedHeader from '@/src/components/FixedHeader';
import Sidebar from '@/src/components/Sidebar';
import ValueCard from '@/src/components/ValueCard';
import AddValueModal from '@/src/components/AddValueModal';
import { useState, useEffect } from 'react';
import { valuesAPI } from '@/services/api';
import toast from 'react-hot-toast';

const ValuesPage = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [values, setValues] = useState([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [showModal, setShowModal] = useState(false);

	// Load values from API
	useEffect(() => {
		const loadValues = async () => {
			try {
				setLoading(true);
				const response = await valuesAPI.getUserValues();
				setValues(response.data.values);
				setError(null);
			} catch (err) {
				console.error('Failed to load values:', err);
				setError('Failed to load values. Please try again.');
			} finally {
				setLoading(false);
			}
		};

		loadValues();
	}, []);

	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

	const addNewValue = async (valueData) => {
		try {
			const response = await valuesAPI.createValue(valueData);
			setValues([response.data.value, ...values]);
			setShowModal(false);
		} catch (error) {
			console.error('Failed to create value:', error);
			setError('Failed to create value. Please try again.');
			toast(error);
		}
	};

	// Handle value deletion - this function will be called by ValueCard
	const handleDeleteValue = (valueId) => {
		setValues((prevValues) =>
			prevValues.filter((value) => value._id !== valueId),
		);
	};

	return (
		<>
			<div className='min-h-screen flex flex-col'>
				<FixedHeader
					title='Values'
					toggleSidebar={toggleSidebar}
				/>

				<div className='flex-1 p-4 md:p-8'>
					<div className='max-w-4xl mx-auto'>
						{/* Hero Section */}
						<div className='text-center mb-12'>
							<div className='inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-full mb-6'>
								<svg
									className='w-8 h-8 text-white'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'
									/>
								</svg>
							</div>
							<h1 className='text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4'>
								Your Core Values
							</h1>
							<p className='text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed'>
								If your life were a story, what core themes would define its
								plot?
							</p>
						</div>

						{/* Loading State */}
						{loading && (
							<div className='text-center py-16'>
								<div className='inline-flex items-center justify-center w-12 h-12 bg-teal-100 dark:bg-teal-900 rounded-full mb-4 animate-pulse'>
									<div className='w-6 h-6 bg-teal-500 rounded-full animate-bounce'></div>
								</div>
								<p className='text-lg text-gray-600 dark:text-gray-300'>
									Loading your values...
								</p>
							</div>
						)}

						{/* Error State */}
						{error && (
							<div className='text-center py-16'>
								<div className='inline-flex items-center justify-center w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full mb-4'>
									<svg
										className='w-6 h-6 text-red-500'
										fill='none'
										stroke='currentColor'
										viewBox='0 0 24 24'>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
										/>
									</svg>
								</div>
								<p className='text-red-600 dark:text-red-400 text-lg mb-4'>
									{error}
								</p>
								<button
									onClick={() => window.location.reload()}
									className='px-6 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors'>
									Try Again
								</button>
							</div>
						)}

						{/* Values Grid */}
						{!loading && !error && values.length > 0 && (
							<div className='grid gap-1 md:gap-2 mb-2'>
								{values.map((val, idx) => (
									<div
										key={val._id || idx}
										className='transform transition-all duration-300 hover:scale-[1.02]'>
										<ValueCard
											title={val.name}
											valueId={val._id}
											description={val.definition}
											onDelete={handleDeleteValue}
										/>
									</div>
								))}
							</div>
						)}

						{/* Empty State */}
						{!loading && !error && values.length === 0 && (
							<div className='text-center py-16'>
								<div className='inline-flex items-center justify-center w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full mb-6'>
									<svg
										className='w-8 h-8 text-gray-400'
										fill='none'
										stroke='currentColor'
										viewBox='0 0 24 24'>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
										/>
									</svg>
								</div>
								<h3 className='text-xl font-semibold text-gray-900 dark:text-white mb-2'>
									No values defined yet
								</h3>
								<p className='text-gray-600 dark:text-gray-400 mb-6'>
									Start building your value system by defining your first core
									value
								</p>
							</div>
						)}

						{/* Add Button */}
						<div className='text-center'>
							<button
								onClick={() => setShowModal(true)}
								className='group inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105'>
								<svg
									className='w-5 h-5 transition-transform group-hover:rotate-90'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M12 6v6m0 0v6m0-6h6m-6 0H6'
									/>
								</svg>
								Add New Value
							</button>
						</div>
					</div>
				</div>

				{showModal && (
					<AddValueModal
						onClose={() => setShowModal(false)}
						onSave={addNewValue}
					/>
				)}

				<Sidebar
					isOpen={isSidebarOpen}
					onToggle={toggleSidebar}
				/>
			</div>
		</>
	);
};

export default ValuesPage;
