import AdminLayout from '@/src/components/AdminLayout';
import {
	FaUsers,
	FaCalendarCheck,
	FaVideo,
	FaUserPlus,
	FaChalkboardTeacher,
	FaCalendarAlt,
	FaComments,
} from 'react-icons/fa';
import React from 'react';

const DashboardCard = ({ icon, title, value, subtitle, children }) => (
	<div className='bg-white rounded-2xl shadow p-4 flex flex-col gap-2'>
		<div className='flex items-center justify-between'>
			<h3 className='text-md font-semibold text-gray-700'>{title}</h3>
			<div className='text-5xl text-gray-600'>{icon}</div>
		</div>
		<h2 className='text-3xl font-bold text-gray-900'>{value}</h2>
		<p className='text-sm text-gray-500'>{subtitle}</p>
		{children}
	</div>
);

const page = () => {
	return (
		<AdminLayout>
			{/* Overall padding (p-6) is now handled by AdminLayout's <main> tag */}
			<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
				{/* TOTAL USERS */}
				<DashboardCard
					title='TOTAL USERS'
					value='100'
					subtitle='+44% from Last Month'
					icon={<FaUsers />}>
					<div className='w-full bg-gray-200 h-2 rounded'>
						<div className='bg-teal-400 h-2 rounded w-[44%]' />
					</div>
				</DashboardCard>

				{/* TOTAL BOOKINGS */}
				<DashboardCard
					title='TOTAL BOOKINGS'
					value='57'
					subtitle='+33% from Last Month'
					icon={<FaCalendarCheck />}>
					<div className='w-full bg-gray-200 h-2 rounded'>
						<div className='bg-teal-400 h-2 rounded w-[33%]' />
					</div>
				</DashboardCard>

				{/* TOTAL CONTENT */}
				<DashboardCard
					title='TOTAL CONTENT'
					value='98'
					subtitle='+39% from Last Month'
					icon={<FaVideo />}>
					<ul className='text-sm text-gray-600 mt-1 list-disc pl-5'>
						<li>27 Courses</li>
						<li>71 Posts</li>
					</ul>
				</DashboardCard>

				{/* RECENT BOOKINGS */}
				<DashboardCard
					title='RECENT BOOKINGS'
					value=''
					subtitle=''
					icon={<FaCalendarAlt />}>
					<div className='text-sm text-gray-600 space-y-1'>
						<p>
							<strong>Name:</strong> Ashley Stones
						</p>
						<p>
							<strong>Date:</strong> 06/04/2025
						</p>
						<p>
							<strong>Time:</strong> 11:00
						</p>
						<p>
							<strong>Status:</strong>{' '}
							<span className='text-green-600 font-medium'>Scheduled</span>
						</p>
					</div>
				</DashboardCard>

				{/* COMMUNITY ACTIVITY */}
				<DashboardCard
					title='COMMUNITY ACTIVITY'
					value=''
					subtitle=''
					icon={<FaComments />}>
					<div className='text-sm text-gray-600 space-y-1'>
						<p>
							<strong>Name:</strong> Ashley Stones
						</p>
						<p>
							<strong>Title:</strong> Ashley’s Goal
						</p>
						<p>
							<strong>Time:</strong> 09/04/2025
						</p>
						<p>
							<strong>Time:</strong> 15:41
						</p>
					</div>
				</DashboardCard>

				{/* NEW USERS */}
				<DashboardCard
					title='NEW USERS'
					value='61'
					subtitle='+62% from Last Month'
					icon={<FaUserPlus />}>
					<div className='w-full bg-gray-200 h-2 rounded'>
						<div className='bg-teal-400 h-2 rounded w-[62%]' />
					</div>
				</DashboardCard>
			</div>

			{/* BUTTONS */}
			{/* Horizontal padding (px-6) was part of the overall p-6 from AdminLayout */}
			<div className='mt-8 flex flex-wrap gap-4'>
				<button className='bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg font-medium'>
					Create New Users
				</button>
				<button className='bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg font-medium'>
					Create New Course
				</button>
				<button className='bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg font-medium'>
					View All Bookings
				</button>
				<button className='bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg font-medium'>
					Moderate Content
				</button>
			</div>
		</AdminLayout>
	);
};

export default page;
