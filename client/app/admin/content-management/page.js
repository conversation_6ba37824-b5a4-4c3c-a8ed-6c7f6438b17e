'use client';
import AdminLayout from '@/src/components/AdminLayout';
import Pagination from '@/src/components/Pagination';
import SearchBar from '@/src/components/SearchBar';
import ProtectedRoute from '@/src/components/ProtectedRoute';
import {
	Table,
	TableBody,
	TableCaption,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/src/components/ui/table';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/src/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/src/components/ui/dialog';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent } from '@/src/components/ui/card';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
	<PERSON>a<PERSON><PERSON><PERSON>,
	FaEllipsisV,
	FaPlus,
	FaEdit,
	FaTrash,
	FaEye,
	FaImage,
	FaUser,
	FaCalendar,
	FaTag,
	FaBookOpen,
	FaComments,
} from 'react-icons/fa';
import { useLessons, useDeleteLesson } from '@/hooks/useLessons';
import toast from 'react-hot-toast';
import { Button } from '@/src/components/common';

const community = [
	{
		id: 1,
		title: 'My thoughts on...',
		author: 'Ashley Stones',
		status: 'Published',
		datePosted: '02/02/2025',
		thumbnail: '/api/placeholder/300/200',
	},
];

const ContentManagement = () => {
	const router = useRouter();
	const [currentPage, setCurrentPage] = useState(1);
	const [activeTab, setActiveTab] = useState('lessons');
	const [searchTerm, setSearchTerm] = useState('');
	const [viewMode, setViewMode] = useState('table'); // 'grid' or 'table'
	const [deleteDialog, setDeleteDialog] = useState({
		isOpen: false,
		type: '', // 'lesson' or 'community'
		item: null,
		title: '',
	});
	const deleteLessonMutation = useDeleteLesson();

	// Fetch lessons from backend
	const {
		data: lessonsData,
		isLoading,
		error,
	} = useLessons({
		page: currentPage,
		limit: 12,
		title: searchTerm,
	});

	const lessons = lessonsData?.data?.data || [];
	const pagination = lessonsData?.data?.pagination || {};

	const currentData = activeTab === 'lessons' ? lessons : community;

	const handleSearch = (term) => {
		setSearchTerm(term);
		setCurrentPage(1);
	};

	const handleEditLesson = (lessonId) => {
		router.push(`/admin/lessons/edit/${lessonId}`);
	};

	const handleViewCommunityPost = (postId) => {
		// Add your community post view logic here
		console.log('Viewing community post:', postId);
	};

	const openDeleteDialog = (type, item) => {
		setDeleteDialog({
			isOpen: true,
			type,
			item,
			title: item.title,
		});
	};

	const closeDeleteDialog = () => {
		setDeleteDialog({
			isOpen: false,
			type: '',
			item: null,
			title: '',
		});
	};

	const handleConfirmDelete = async () => {
		const { type, item } = deleteDialog;

		try {
			if (type === 'lesson') {
				await deleteLessonMutation.mutateAsync(item.id || item._id);
				toast('Lesson deleted successfully!');
			} else if (type === 'community') {
				// Add your community post delete logic here
				console.log('Deleting community post:', item.id);
				toast('Community post deleted successfully!');
			}
		} catch (error) {
			console.error('Delete error:', error);
			toast(error?.response?.data?.message || `Failed to delete ${type}`);
		} finally {
			closeDeleteDialog();
		}
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-GB');
	};

	const getStatusBadgeColor = (status) => {
		switch (status?.toLowerCase()) {
			case 'published':
				return 'text-teal-600 bg-teal-100 px-3 py-1 rounded-full text-xs font-medium';
			case 'draft':
				return 'text-yellow-600 bg-yellow-100 px-3 py-1 rounded-full text-xs font-medium';
			case 'archived':
				return 'text-gray-600 bg-gray-100 px-3 py-1 rounded-full text-xs font-medium';
			default:
				return 'text-teal-600 bg-teal-100 px-3 py-1 rounded-full text-xs font-medium';
		}
	};

	const renderGridView = () => (
		<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
			{currentData.length === 0 ? (
				<div className='col-span-full text-center py-12'>
					<div className='text-gray-400 mb-4'>
						{activeTab === 'lessons' ? (
							<FaBookOpen size={48} />
						) : (
							<FaComments size={48} />
						)}
					</div>
					<p className='text-gray-500 text-lg'>No {activeTab} found</p>
				</div>
			) : (
				currentData.map((item, index) => (
					<Card
						key={item.id || index}
						className='group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-teal-300'>
						<CardContent className='p-0'>
							{/* Thumbnail */}
							<div className='relative h-48 bg-gray-100 rounded-t-lg overflow-hidden'>
								{item.thumbnail ? (
									<img
										src={item.thumbnail}
										alt={item.title}
										className='w-full h-full object-cover group-hover:scale-105 transition-transform duration-300'
									/>
								) : (
									<div className='w-full h-full bg-gradient-to-br from-teal-100 to-cyan-100 flex items-center justify-center'>
										<FaImage className='text-teal-400 text-4xl' />
									</div>
								)}

								{/* Status Badge */}
								<div className='absolute top-3 right-3'>
									<span className={getStatusBadgeColor('published')}>
										Published
									</span>
								</div>

								{/* Actions Overlay */}
								<div className='absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center'>
									<div className='flex gap-2'>
										{activeTab === 'community' && (
											<Button
												size='sm'
												onClick={() => handleViewCommunityPost(item.id)}
												className='bg-white text-teal-600 hover:bg-teal-50'>
												<FaEye className='h-4 w-4' />
											</Button>
										)}
										<Button
											size='sm'
											onClick={() => {
												if (activeTab === 'lessons') {
													handleEditLesson(item.id || item._id);
												} else {
													console.log('Edit community post:', item.id);
												}
											}}
											className='bg-white text-teal-600 hover:bg-teal-50'>
											<FaEdit className='h-4 w-4' />
										</Button>
										<Button
											size='sm'
											onClick={() =>
												openDeleteDialog(
													activeTab === 'lessons' ? 'lesson' : 'community',
													item,
												)
											}
											className='bg-white text-red-600 hover:bg-red-50'>
											<FaTrash className='h-4 w-4' />
										</Button>
									</div>
								</div>
							</div>

							{/* Content */}
							<div className='p-4'>
								<h3 className='font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-teal-600 transition-colors'>
									{item.title}
								</h3>

								<div className='flex items-center gap-2 text-sm text-gray-600 mb-2'>
									<FaUser className='h-3 w-3' />
									<span>
										{activeTab === 'lessons'
											? item.instructor || item.author
											: item.author}
									</span>
								</div>

								{activeTab === 'lessons' && item.category && (
									<div className='flex items-center gap-2 text-sm text-gray-600 mb-2'>
										<FaTag className='h-3 w-3' />
										<span>{item.category}</span>
									</div>
								)}

								<div className='flex items-center gap-2 text-sm text-gray-600'>
									<FaCalendar className='h-3 w-3' />
									<span>
										{activeTab === 'lessons'
											? formatDate(item.createdAt || item.datePosted)
											: item.datePosted}
									</span>
								</div>
							</div>
						</CardContent>
					</Card>
				))
			)}
		</div>
	);

	const renderTableView = () => (
		<div className='bg-white rounded-lg shadow-sm border border-gray-200'>
			<Table>
				<TableHeader>
					<TableRow className='bg-teal-50 hover:bg-teal-50'>
						<TableHead className='font-semibold text-teal-800'>
							Thumbnail
						</TableHead>
						<TableHead className='font-semibold text-teal-800'>Title</TableHead>
						<TableHead className='font-semibold text-teal-800'>
							{activeTab === 'lessons' ? 'Instructor' : 'Author'}
						</TableHead>
						{activeTab === 'lessons' && (
							<TableHead className='font-semibold text-teal-800'>
								Category
							</TableHead>
						)}
						<TableHead className='font-semibold text-teal-800'>
							Status
						</TableHead>
						<TableHead className='font-semibold text-teal-800'>
							{activeTab === 'lessons' ? 'Created' : 'Date Posted'}
						</TableHead>
						<TableHead className='font-semibold text-teal-800'>
							Actions
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{currentData.length === 0 ? (
						<TableRow>
							<TableCell
								colSpan={activeTab === 'lessons' ? 7 : 6}
								className='text-center py-12'>
								<div className='flex flex-col items-center gap-4'>
									<div className='text-gray-400'>
										{activeTab === 'lessons' ? (
											<FaBookOpen size={48} />
										) : (
											<FaComments size={48} />
										)}
									</div>
									<p className='text-gray-500 text-lg'>No {activeTab} found</p>
								</div>
							</TableCell>
						</TableRow>
					) : (
						currentData.map((item, index) => (
							<TableRow
								key={item.id || index}
								className='hover:bg-teal-50 transition-colors border-b border-gray-100'>
								<TableCell className='py-3'>
									<div className='w-16 h-12 bg-gray-100 rounded-md overflow-hidden'>
										{item.thumbnail ? (
											<img
												src={item.thumbnail}
												alt={item.title}
												className='w-full h-full object-cover'
											/>
										) : (
											<div className='w-full h-full bg-gradient-to-br from-teal-100 to-cyan-100 flex items-center justify-center'>
												<FaImage className='text-teal-400 text-sm' />
											</div>
										)}
									</div>
								</TableCell>
								<TableCell className='font-medium text-gray-800 max-w-xs'>
									<div className='truncate'>{item.title}</div>
								</TableCell>
								<TableCell className='text-gray-600'>
									{activeTab === 'lessons'
										? item.instructor || item.author
										: item.author}
								</TableCell>
								{activeTab === 'lessons' && (
									<TableCell className='text-gray-600'>
										{item.category || 'General'}
									</TableCell>
								)}
								<TableCell>
									<span className={getStatusBadgeColor('published')}>
										Published
									</span>
								</TableCell>
								<TableCell className='text-gray-600'>
									{activeTab === 'lessons'
										? formatDate(item.createdAt || item.datePosted)
										: item.datePosted}
								</TableCell>
								<TableCell>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button
												size='sm'
												className='p-2 hover:bg-teal-100 text-gray-600 hover:text-teal-600'>
												<FaEllipsisV />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent
											align='end'
											className='w-40 backdrop-blur-sm'>
											{activeTab === 'community' && (
												<DropdownMenuItem
													onClick={() => handleViewCommunityPost(item.id)}
													className='flex items-center gap-2 text-teal-600 cursor-pointer'>
													<FaEye className='h-3 w-3' />
													View
												</DropdownMenuItem>
											)}
											<DropdownMenuItem
												onClick={() => {
													if (activeTab === 'lessons') {
														handleEditLesson(item.id || item._id);
													} else {
														console.log('Edit community post:', item.id);
													}
												}}
												className='flex items-center gap-2 text-teal-600 cursor-pointer'>
												<FaEdit className='h-3 w-3' />
												Edit
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() =>
													openDeleteDialog(
														activeTab === 'lessons' ? 'lesson' : 'community',
														item,
													)
												}
												className='flex items-center gap-2 text-red-600 cursor-pointer'>
												<FaTrash className='h-3 w-3' />
												Delete
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</TableCell>
							</TableRow>
						))
					)}
				</TableBody>
			</Table>
		</div>
	);

	if (isLoading) {
		return (
			<AdminLayout>
				<div className='p-6 bg-gradient-to-br from-cyan-50 to-teal-50 min-h-screen'>
					<div className='flex justify-center items-center h-64'>
						<div className='animate-spin rounded-full h-12 w-12 border-4 border-teal-200 border-t-teal-600'></div>
					</div>
				</div>
			</AdminLayout>
		);
	}

	if (error) {
		return (
			<AdminLayout>
				<div className='p-6 bg-gradient-to-br from-cyan-50 to-teal-50 min-h-screen'>
					<div className='text-center'>
						<div className='bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto'>
							<div className='text-red-600 mb-2'>⚠️</div>
							<h3 className='text-red-800 font-semibold mb-2'>
								Error Loading Data
							</h3>
							<p className='text-red-600'>{error.message}</p>
						</div>
					</div>
				</div>
			</AdminLayout>
		);
	}

	return (
		<AdminLayout>
			<div className='p-6 bg-gradient-to-br from-cyan-50 to-teal-50 min-h-screen'>
				{/* Header */}
				<div className='mb-8'>
					<h1 className='text-3xl font-bold text-gray-800 mb-2'>
						Content Management
					</h1>
					<p className='text-gray-600'>
						Manage your lessons and community posts
					</p>
				</div>

				{/* Controls */}
				<div className='bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6'>
					<div className='flex flex-wrap justify-between items-center gap-4'>
						<div className='flex flex-wrap gap-4 items-center'>
							{/* Tabs */}
							<div className='flex bg-gray-100 rounded-lg p-1'>
								{['lessons', 'community'].map((tab) => (
									<button
										key={tab}
										onClick={() => setActiveTab(tab)}
										className={`px-4 py-2 text-sm font-medium rounded-md transition-all ${
											activeTab === tab
												? 'bg-teal-600 text-white shadow-sm'
												: 'text-gray-700 hover:bg-white hover:shadow-sm'
										}`}>
										{tab.charAt(0).toUpperCase() + tab.slice(1)}
									</button>
								))}
							</div>

							{/* View Mode Toggle */}
							<div className='flex bg-gray-100 rounded-lg p-1'>
								<button
									onClick={() => setViewMode('grid')}
									className={`px-3 py-2 text-sm font-medium rounded-md transition-all ${
										viewMode === 'grid'
											? 'bg-teal-600 text-white shadow-sm'
											: 'text-gray-700 hover:bg-white hover:shadow-sm'
									}`}>
									Grid
								</button>
								<button
									onClick={() => setViewMode('table')}
									className={`px-3 py-2 text-sm font-medium rounded-md transition-all ${
										viewMode === 'table'
											? 'bg-teal-600 text-white shadow-sm'
											: 'text-gray-700 hover:bg-white hover:shadow-sm'
									}`}>
									Table
								</button>
							</div>

							{/* Search */}
							{activeTab === 'lessons' && (
								<div className='min-w-80'>
									<SearchBar
										placeholder='Search lessons...'
										onSearch={handleSearch}
										value={searchTerm}
									/>
								</div>
							)}
						</div>

						{activeTab === 'lessons' && (
							<Button
								onClick={() => router.push('/admin/lessons/create')}
								className='bg-teal-600 hover:bg-teal-700 text-white px-6 py-2 rounded-lg shadow-sm transition-all hover:shadow-md flex items-center gap-2'>
								<FaPlus className='h-4 w-4' />
								Create New Lesson
							</Button>
						)}
					</div>
				</div>

				{/* Content */}
				<div className='mb-6'>
					{viewMode === 'grid' ? renderGridView() : renderTableView()}
				</div>

				{/* Pagination */}
				{activeTab === 'lessons' && pagination && (
					<div className='flex justify-center'>
						<Pagination
							currentPage={currentPage}
							totalPages={pagination.totalPages || 1}
							onPageChange={setCurrentPage}
						/>
					</div>
				)}

				{/* Delete Confirmation Dialog */}
				<Dialog
					open={deleteDialog.isOpen}
					onOpenChange={closeDeleteDialog}>
					<DialogContent className='sm:max-w-[425px] bg-teal-600 text-white'>
						<DialogHeader>
							<DialogTitle className='flex items-center gap-2 text-white'>
								<FaTrash className='text-red-300' />
								Delete{' '}
								{deleteDialog.type === 'lesson' ? 'Lesson' : 'Community Post'}
							</DialogTitle>
							<DialogDescription className='text-teal-100'>
								Are you sure you want to delete "
								<span className='font-semibold'>{deleteDialog.title}</span>"?
								This action cannot be undone.
							</DialogDescription>
						</DialogHeader>
						<DialogFooter className='flex gap-2 pt-4'>
							<Button
								variant='outline'
								onClick={closeDeleteDialog}
								className='text-white hover:bg-teal-500'>
								Cancel
							</Button>
							<Button
								onClick={handleConfirmDelete}
								className='bg-red-500 hover:bg-red-600 text-white'>
								Delete
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			</div>
		</AdminLayout>
	);
};

export default ContentManagement;
