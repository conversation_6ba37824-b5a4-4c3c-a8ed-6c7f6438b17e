'use client';
import ProtectedRoute from '@/src/components/ProtectedRoute';
import LessonEditForm from '@/src/components/LessonEditForm';
import { use } from 'react';

export default function EditLessonPage({ params }) {
	const unwrappedParams = use(params);
	return (
		<ProtectedRoute requiredRoles={['superadmin', 'tutor']}>
			<LessonEditForm lessonId={unwrappedParams.id} />
		</ProtectedRoute>
	);
}
