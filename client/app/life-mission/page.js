'use client';
import { sunset } from '@/assets/images';
import CustomImage from '@/src/components/CustomImage';
import FixedHeader from '@/src/components/FixedHeader';
import Sidebar from '@/src/components/Sidebar';
import React, { useState } from 'react';

const LifeMission = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [visibleOnProfile, setVisibleOnProfile] = useState(false);
	const [shareOnCommunity, setShareOnCommunity] = useState(false);

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	return (
		<div className='flex flex-col min-h-screen'>
			<FixedHeader
				title='Life Mission'
				toggleSidebar={toggleSidebar}
			/>

			<div className='flex justify-center items-center w-full py-6'>
				<div className='w-full max-w-2xl rounded-none md:rounded-xl shadow-xl overflow-hidden'>
					<CustomImage
						src={sunset}
						alt='Sunset'
						width={800}
						height={300}
						className='object-cover w-full h-52 md:h-64'
					/>

					<div className='flex flex-col px-4 py-6 text-black dark:text-white'>
						<h4 className='text-xl font-semibold'>
							What does your ideal future look like?
						</h4>
						<p className='text-lg text-gray-500 font-semibold'>Hint</p>
						<p>In 5/10/20 years, what kind of impact do you want to have?</p>
						<p>What are the most important experiences you want to create?</p>

						<textarea
							className='w-full mt-4 p-3 rounded-lg dark:bg-gray-400 shadow-md border-2 border-black text-black outline-none resize-none placeholder:text-gray-300 placeholder:text-lg placeholder:italic'
							placeholder='Type something!'
							rows={6}
						/>

						<div className='flex flex-col gap-2 mt-3 text-sm'>
							<label className='flex items-center gap-2'>
								<input
									type='checkbox'
									checked={visibleOnProfile}
									onChange={() => setVisibleOnProfile(!visibleOnProfile)}
									className='form-checkbox accent-teal-500'
								/>
								<span>Make visible on profile</span>
							</label>

							<label className='flex items-center gap-2'>
								<input
									type='checkbox'
									checked={shareOnCommunity}
									onChange={() => setShareOnCommunity(!shareOnCommunity)}
									className='form-checkbox accent-teal-500'
								/>
								<span>Share on community</span>
							</label>
						</div>

						<div className='flex justify-end items-center mt-4'>
							<button className='w-24 text-xl dark:bg-teal-600 bg-[#0C142A] text-white py-2 rounded-lg font-semibold'>
								Save
							</button>
						</div>
					</div>
				</div>
			</div>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default LifeMission;
