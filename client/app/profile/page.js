'use client';
import CustomImage from '@/src/components/CustomImage';
import FixedHeader from '@/src/components/FixedHeader';
import Sidebar from '@/src/components/Sidebar';
import { useUserProfile } from '@/hooks/useUser';
import { useDarkMode } from '@/contexts/DarkModeContext';
import React, { useState } from 'react';
import {
	FaUserEdit,
	FaBell,
	FaShieldAlt,
	FaMoon,
	FaUserPlus,
} from 'react-icons/fa'; // Importing icons

const Profile = () => {
	const {
		data: userProfile,
		isLoading: isUserLoading,
		error: userError,
	} = useUserProfile();
	const { isDarkMode, toggleDarkMode } = useDarkMode();
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	return (
		<div className='min-h-screen bg-white dark:bg-[#0C142A] transition-colors duration-300'>
			<FixedHeader
				title='Profile'
				toggleSidebar={toggleSidebar}
			/>
			<div className='container mx-auto px-4 py-8 pt-20'>
				{' '}
				{/* Added padding-top to account for fixed header */}
				<div className='flex flex-col items-center mb-8'>
					<div className='w-24 h-24 rounded-full overflow-hidden shadow-lg ring-4 ring-white dark:ring-gray-800 hover:ring-teal-200 dark:hover:ring-teal-800 transition-all duration-200 cursor-pointer relative'>
						<CustomImage
							src={userProfile?.user?.avatar} // Fallback placeholder image
							alt='User Avatar'
							className='w-full h-full object-cover hover:scale-110 transition-transform duration-200'
						/>
						{/* Edit icon for the profile picture - similar to the design's green dot */}
						<div className='absolute bottom-0 right-0 bg-teal-500 rounded-full p-1 border-2 border-white dark:border-gray-900'>
							<FaUserEdit className='text-white text-xs' />
						</div>
					</div>
					{/* You can add user's name or other info here if needed */}
					{/* <p className='mt-4 text-lg font-semibold text-gray-800 dark:text-gray-200'>{userProfile?.user?.name || 'User Name'}</p> */}
				</div>
				<div className='rounded-lg shadow-md overflow-hidden bg-white dark:bg-gray-800 transition-colors duration-300'>
					<ul>
						{/* Edit Profile */}
						<li className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200'>
							<div className='flex items-center'>
								<FaUserEdit className='text-gray-600 dark:text-gray-400 mr-4 text-xl' />
								<span className='text-lg text-gray-800 dark:text-gray-200'>
									Edit Profile
								</span>
							</div>
							<svg
								className='w-5 h-5 text-gray-400 dark:text-gray-500'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'
								xmlns='http://www.w3.org/2000/svg'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth='2'
									d='M9 5l7 7-7 7'></path>
							</svg>
						</li>

						{/* Notifications */}
						<li className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200'>
							<div className='flex items-center'>
								<FaBell className='text-gray-600 dark:text-gray-400 mr-4 text-xl' />
								<span className='text-gray-800 dark:text-gray-200 text-lg'>
									Notifications
								</span>
							</div>
							<svg
								className='w-5 h-5 text-gray-400 dark:text-gray-500'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'
								xmlns='http://www.w3.org/2000/svg'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth='2'
									d='M9 5l7 7-7 7'></path>
							</svg>
						</li>

						{/* Security */}
						<li className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200'>
							<div className='flex items-center'>
								<FaShieldAlt className='text-gray-600 dark:text-gray-400 mr-4 text-xl' />
								<span className='text-gray-800 dark:text-gray-200 text-lg'>
									Security
								</span>
							</div>
							<svg
								className='w-5 h-5 text-gray-400 dark:text-gray-500'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'
								xmlns='http://www.w3.org/2000/svg'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth='2'
									d='M9 5l7 7-7 7'></path>
							</svg>
						</li>

						{/* Dark Mode */}
						{/* <li className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700'>
							<div className='flex items-center'>
								<FaMoon className='text-gray-600 dark:text-gray-400 mr-4 text-xl' />
								<span className='text-gray-800 dark:text-gray-200 text-lg'>
									Dark Mode
								</span>
							</div>
							
							<label
								htmlFor='darkModeToggle'
								className='flex items-center cursor-pointer'>
								<div className='relative'>
									<input
										type='checkbox'
										id='darkModeToggle'
										className='sr-only'
										checked={isDarkMode}
										onChange={toggleDarkMode}
									/>
									<div className={`block w-14 h-8 rounded-full transition-colors duration-300 ${
										isDarkMode ? 'bg-teal-500' : 'bg-gray-300'
									}`}></div>
									<div
										className={`dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition-transform duration-300 ${
											isDarkMode ? 'translate-x-full' : ''
										}`}></div>
								</div>
							</label>
						</li> */}

						{/* Invite Friends */}
						<li className='flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200'>
							<div className='flex items-center'>
								<FaUserPlus className='text-gray-600 dark:text-gray-400 mr-4 text-xl' />
								<span className='text-gray-800 dark:text-gray-200 text-lg'>
									Invite Friends
								</span>
							</div>
							<svg
								className='w-5 h-5 text-gray-400 dark:text-gray-500'
								fill='none'
								stroke='currentColor'
								viewBox='0 0 24 24'
								xmlns='http://www.w3.org/2000/svg'>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth='2'
									d='M9 5l7 7-7 7'></path>
							</svg>
						</li>
					</ul>
				</div>
			</div>
			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Profile;
