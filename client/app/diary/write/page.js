'use client';
import FixedHeader from '@/src/components/FixedHeader';
import Sidebar from '@/src/components/Sidebar';
import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
	createDiaryEntry,
	getDiaryEntryByDate,
	updateDiaryEntry,
	uploadImage,
} from '@/api/diary';
import {
	FaPlus,
	FaImage,
	FaCalendar,
	FaArrowLeft,
	FaUpload,
	FaTimes,
	FaEdit,
	FaSave,
} from 'react-icons/fa';
import toast from 'react-hot-toast';

// Separate component that uses useSearchParams
const WriteContent = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [title, setTitle] = useState('');
	const [story, setStory] = useState('');
	const [selectedDate, setSelectedDate] = useState('');
	const [image, setImage] = useState(null);
	const [imagePreview, setImagePreview] = useState(null);
	const [loading, setLoading] = useState(false);
	const [pageLoading, setPageLoading] = useState(true);
	const [imageUploading, setImageUploading] = useState(false);
	const [error, setError] = useState('');
	const [existingEntry, setExistingEntry] = useState(null);
	const [isEditing, setIsEditing] = useState(false);

	const router = useRouter();
	const searchParams = useSearchParams();

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	// Get date from URL params or use today's date
	useEffect(() => {
		const dateParam = searchParams.get('date');
		const dateToUse = dateParam || new Date().toISOString().split('T')[0];
		setSelectedDate(dateToUse);

		// Check if entry exists for this date
		const checkExistingEntry = async () => {
			try {
				setPageLoading(true);
				const response = await getDiaryEntryByDate(dateToUse);
				if (response && response.data) {
					const entry = response.data.entry;
					setExistingEntry(entry);
					setTitle(entry.title);
					setStory(entry.story);
					setIsEditing(true);
					if (entry.image) {
						setImagePreview(entry.image);
					}
					toast.success('Entry loaded for editing');
				}
			} catch (error) {
				// No entry exists for this date, which is fine
				console.log('No existing entry for this date');
			} finally {
				setPageLoading(false);
			}
		};

		checkExistingEntry();
	}, [searchParams]);

	// Handle image selection
	const handleImageSelect = async (event) => {
		const file = event.target.files[0];
		if (file) {
			// Validate file size (max 5MB)
			if (file.size > 5 * 1024 * 1024) {
				toast.error('Image size must be less than 5MB');
				return;
			}

			// Create preview URL immediately
			const previewUrl = URL.createObjectURL(file);
			setImagePreview(previewUrl);
			setImageUploading(true);

			// Upload image to Cloudinary
			try {
				const uploadResponse = await uploadImage(file);
				if (uploadResponse.success) {
					// Replace preview with Cloudinary URL
					setImagePreview(uploadResponse.data.url);
					setImage(uploadResponse.data.url); // Store the URL instead of file
					// Clean up local URL
					URL.revokeObjectURL(previewUrl);
					toast.success('Image uploaded successfully');
				}
			} catch (error) {
				console.error('Error uploading image:', error);
				setError('Failed to upload image. Please try again.');
				toast.error('Failed to upload image');
				// Keep local preview if upload fails
				setImage(file);
			} finally {
				setImageUploading(false);
			}
		}
	};

	// Handle image removal
	const handleRemoveImage = () => {
		setImage(null);
		setImagePreview(null);
		toast.success('Image removed');
	};

	// Handle form submission
	const handleSubmit = async () => {
		if (!title.trim()) {
			setError('Please enter a title for your entry');
			toast.error('Please enter a title');
			return;
		}

		if (!story.trim()) {
			setError('Please write your story');
			toast.error('Please write your story');
			return;
		}

		try {
			setLoading(true);
			setError('');

			const entryData = {
				title: title.trim(),
				story: story.trim(),
				date: selectedDate,
				image: image, // This will be the Cloudinary URL or null
			};

			if (isEditing && existingEntry) {
				// Update existing entry
				await updateDiaryEntry(existingEntry._id, entryData);
				toast.success('Entry updated successfully');
			} else {
				// Create new entry
				await createDiaryEntry(entryData);
				toast.success('Entry saved successfully');
			}

			// Navigate back to diary page
			router.push('/diary');
		} catch (error) {
			console.error('Error saving entry:', error);
			const errorMessage = error.message || 'Failed to save entry';
			setError(errorMessage);
			toast.error(errorMessage);
		} finally {
			setLoading(false);
		}
	};

	// Handle back navigation
	const handleBack = () => {
		if (title.trim() || story.trim()) {
			if (
				confirm('You have unsaved changes. Are you sure you want to leave?')
			) {
				router.push('/diary');
			}
		} else {
			router.push('/diary');
		}
	};

	// Format date for display
	const formatDisplayDate = (dateString) => {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			weekday: 'long',
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	// Character count for story
	const storyCharCount = story.length;

	// Skeleton component
	const SkeletonLoader = () => (
		<div className='bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden animate-pulse'>
			{/* Header Section Skeleton */}
			<div className='p-6 pb-4 border-b border-white/10'>
				{/* Date Display Skeleton */}
				<div className='text-center mb-6'>
					<div className='flex items-center justify-center space-x-2 mb-2'>
						<div className='w-4 h-4 bg-white/10 rounded'></div>
						<div className='w-32 h-4 bg-white/10 rounded'></div>
					</div>
					<div className='w-64 h-6 bg-white/10 rounded mx-auto'></div>
				</div>

				{/* Status Badge Skeleton */}
				<div className='flex justify-center mb-4'>
					<div className='w-24 h-8 bg-white/10 rounded-full'></div>
				</div>
			</div>

			{/* Form Section Skeleton */}
			<div className='p-6 space-y-6'>
				{/* Title Input Skeleton */}
				<div className='space-y-3'>
					<div className='flex items-center space-x-2'>
						<div className='w-4 h-4 bg-white/10 rounded'></div>
						<div className='w-12 h-4 bg-white/10 rounded'></div>
					</div>
					<div className='w-full h-14 bg-white/10 rounded-xl'></div>
				</div>

				{/* Story Input Skeleton */}
				<div className='space-y-3'>
					<div className='flex items-center justify-between'>
						<div className='flex items-center space-x-2'>
							<div className='w-2 h-2 bg-white/10 rounded-full'></div>
							<div className='w-20 h-4 bg-white/10 rounded'></div>
						</div>
						<div className='w-24 h-3 bg-white/10 rounded'></div>
					</div>
					<div className='w-full h-80 bg-white/10 rounded-xl'></div>
				</div>

				{/* Image Section Skeleton */}
				<div className='space-y-3'>
					<div className='flex items-center space-x-2'>
						<div className='w-4 h-4 bg-white/10 rounded'></div>
						<div className='w-20 h-4 bg-white/10 rounded'></div>
					</div>
					<div className='w-full h-32 bg-white/10 rounded-xl'></div>
				</div>
			</div>

			{/* Action Buttons Skeleton */}
			<div className='p-6 pt-0'>
				<div className='flex items-center justify-between'>
					<div className='w-12 h-12 bg-white/10 rounded-full'></div>
					<div className='w-32 h-12 bg-white/10 rounded-xl'></div>
				</div>
			</div>
		</div>
	);

	return (
		<div className='flex flex-col min-h-screen'>
			<FixedHeader
				title={isEditing ? 'Edit Entry' : 'Write Entry'}
				toggleSidebar={toggleSidebar}
			/>

			<div className='flex-1 overflow-hidden'>
				<div className='max-w-4xl mx-auto px-4 py-6'>
					{/* Show skeleton while loading */}
					{pageLoading ? (
						<SkeletonLoader />
					) : (
						/* Main Content Container */
						<div className='bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden'>
							{/* Header Section */}
							<div className='p-6 pb-4 border-b border-white/10'>
								{/* Date Display */}
								{selectedDate && (
									<div className='text-center mb-6'>
										<div className='flex items-center justify-center space-x-2 mb-2'>
											<FaCalendar className='text-[#00A3A3]' />
											<p className='text-white/70 text-sm'>
												{isEditing ? 'Editing entry for' : 'Writing for'}
											</p>
										</div>
										<p className='text-white text-xl font-semibold'>
											{formatDisplayDate(selectedDate)}
										</p>
									</div>
								)}

								{/* Status Badge */}
								<div className='flex justify-center mb-4'>
									<div
										className={`px-4 py-2 rounded-full text-sm font-medium ${
											isEditing
												? 'bg-[#00A3A3]/20 text-[#00A3A3] border border-[#00A3A3]/30'
												: 'bg-[#008080]/20 text-[#008080] border border-[#008080]/30'
										}`}>
										{isEditing ? 'Editing Mode' : 'New Entry'}
									</div>
								</div>

								{/* Error Message */}
								{error && (
									<div className='bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-4'>
										<p className='text-red-400 text-sm text-center'>{error}</p>
									</div>
								)}
							</div>

							{/* Form Section */}
							<div className='md:p-6 p-2 space-y-6'>
								{/* Title Input */}
								<div className='space-y-3'>
									<label className='text-white text-sm font-medium flex items-center space-x-2'>
										<FaEdit className='text-[#00A3A3]' />
										<span>Title</span>
									</label>
									<input
										type='text'
										value={title}
										onChange={(e) => setTitle(e.target.value)}
										placeholder='Add a title to your entry...'
										className='w-full bg-white/5 border border-white/10 rounded-xl
												text-white placeholder-white/50 py-4 px-6 
												focus:outline-none focus:border-[#00A3A3] 
												focus:bg-white/10 transition-all duration-300'
									/>
								</div>

								{/* Story Input */}
								<div className='space-y-3'>
									<div className='flex items-center justify-between'>
										<label className='text-white text-sm font-medium flex items-center space-x-2'>
											<span className='w-2 h-2 bg-[#00A3A3] rounded-full'></span>
											<span>Your Story</span>
										</label>
										<span className='text-white/50 text-xs'>
											{storyCharCount} characters
										</span>
									</div>
									<textarea
										value={story}
										onChange={(e) => setStory(e.target.value)}
										placeholder='Write your story here... Share your thoughts, experiences, and memories.'
										rows={12}
										className='w-full bg-white/5 border border-white/10 rounded-xl
												text-white placeholder-white/50 py-4 px-6 
												focus:outline-none focus:border-[#00A3A3] 
												focus:bg-white/10 transition-all duration-300
												resize-none'
									/>
								</div>

								{/* Image Section */}
								<div className='space-y-3'>
									<label className='text-white text-sm font-medium flex items-center space-x-2'>
										<FaImage className='text-[#00A3A3]' />
										<span>Add Image</span>
									</label>

									{/* Image Preview */}
									{imagePreview && (
										<div className='relative rounded-xl overflow-hidden bg-gray-800/50'>
											<img
												src={imagePreview}
												alt='Preview'
												className='w-full max-h-96 object-cover'
											/>
											{imageUploading && (
												<div className='absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center'>
													<div className='text-center text-white'>
														<div className='w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-2'></div>
														<div className='text-sm'>Uploading...</div>
													</div>
												</div>
											)}
											<button
												onClick={handleRemoveImage}
												className='absolute top-4 right-4 w-8 h-8 bg-red-500/80 hover:bg-red-500 
														text-white rounded-full flex items-center justify-center 
														transition-colors'>
												<FaTimes className='text-sm' />
											</button>
										</div>
									)}

									{/* Image Upload Button */}
									{!imagePreview && (
										<div className='relative'>
											<input
												type='file'
												accept='image/*'
												onChange={handleImageSelect}
												className='absolute inset-0 w-full h-full opacity-0 cursor-pointer'
											/>
											<div
												className='bg-white/5 border-2 border-dashed border-white/20 rounded-xl 
													p-8 text-center hover:bg-white/10 hover:border-[#00A3A3]/50 
													transition-all duration-300 cursor-pointer'>
												<FaUpload className='text-[#00A3A3] text-2xl mx-auto mb-3' />
												<p className='text-white text-sm mb-1'>
													Click to upload an image
												</p>
												<p className='text-white/50 text-xs'>
													PNG, JPG up to 5MB
												</p>
											</div>
										</div>
									)}
								</div>
							</div>

							{/* Action Buttons */}
							<div className='p-6 pt-0'>
								<div className='flex items-center justify-between'>
									<div className='flex items-center space-x-4'>
										{/* Image Upload Alternative Button */}
										{!imagePreview && (
											<div className='relative'>
												<input
													type='file'
													accept='image/*'
													onChange={handleImageSelect}
													className='absolute inset-0 w-full h-full opacity-0 cursor-pointer'
												/>
												<button
													className='w-12 h-12 bg-white/10 hover:bg-white/20 rounded-full 
														flex items-center justify-center transition-all duration-300
														border border-white/20 hover:border-[#00A3A3]/50'>
													<FaImage className='text-[#00A3A3] text-lg' />
												</button>
											</div>
										)}
									</div>

									{/* Submit Button */}
									<button
										onClick={handleSubmit}
										disabled={loading || !title.trim() || !story.trim()}
										className='bg-[#00A3A3] hover:bg-[#008080] disabled:bg-gray-600 
												text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300
												disabled:cursor-not-allowed disabled:opacity-60
												flex items-center space-x-2 transform hover:scale-105'>
										{loading ? (
											<>
												<div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
												<span>Saving...</span>
											</>
										) : (
											<>
												<FaSave />
												<span>{isEditing ? 'Update Entry' : 'Save Entry'}</span>
											</>
										)}
									</button>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

// Loading fallback component
const WriteFallback = () => (
	<div className='flex flex-col min-h-screen'>
		<FixedHeader
			title='Write Entry'
			toggleSidebar={() => {}}
		/>
		<div className='flex-1 overflow-hidden'>
			<div className='max-w-4xl mx-auto px-4 py-6'>
				<div className='bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden animate-pulse'>
					<div className='p-6 pb-4 border-b border-white/10'>
						<div className='text-center mb-6'>
							<div className='flex items-center justify-center space-x-2 mb-2'>
								<div className='w-4 h-4 bg-white/10 rounded'></div>
								<div className='w-32 h-4 bg-white/10 rounded'></div>
							</div>
							<div className='w-64 h-6 bg-white/10 rounded mx-auto'></div>
						</div>
						<div className='flex justify-center mb-4'>
							<div className='w-24 h-8 bg-white/10 rounded-full'></div>
						</div>
					</div>
					<div className='p-6 space-y-6'>
						<div className='space-y-3'>
							<div className='flex items-center space-x-2'>
								<div className='w-4 h-4 bg-white/10 rounded'></div>
								<div className='w-12 h-4 bg-white/10 rounded'></div>
							</div>
							<div className='w-full h-14 bg-white/10 rounded-xl'></div>
						</div>
						<div className='space-y-3'>
							<div className='flex items-center justify-between'>
								<div className='flex items-center space-x-2'>
									<div className='w-2 h-2 bg-white/10 rounded-full'></div>
									<div className='w-20 h-4 bg-white/10 rounded'></div>
								</div>
								<div className='w-24 h-3 bg-white/10 rounded'></div>
							</div>
							<div className='w-full h-80 bg-white/10 rounded-xl'></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
);

// Main component with Suspense boundary
const Write = () => {
	return (
		<Suspense fallback={<WriteFallback />}>
			<WriteContent />
		</Suspense>
	);
};

export default Write;
