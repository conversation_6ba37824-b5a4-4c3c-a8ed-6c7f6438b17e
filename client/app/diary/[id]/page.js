'use client';
import { diary } from '@/assets/images';
import FixedHeader from '@/src/components/FixedHeader';
import Sidebar from '@/src/components/Sidebar';
import { useRouter, useParams } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { getDiaryEntryById, deleteDiaryEntry } from '@/api/diary';
import {
	FaEdit,
	FaTrash,
	FaCalendar,
	FaArrowLeft,
	FaImage,
} from 'react-icons/fa';
import toast from 'react-hot-toast';

const DiaryEntryDetails = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [entry, setEntry] = useState(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState('');
	const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
	const [deleting, setDeleting] = useState(false);

	const router = useRouter();
	const params = useParams();
	const entryId = params.id;

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	// Fetch entry details
	useEffect(() => {
		const fetchEntry = async () => {
			try {
				setLoading(true);
				const response = await getDiaryEntryById(entryId);
				setEntry(response.data.entry);
			} catch (error) {
				console.error('Error fetching entry:', error);
				setError('Failed to load diary entry');
				toast.error('Failed to load diary entry');
			} finally {
				setLoading(false);
			}
		};

		if (entryId) {
			fetchEntry();
		}
	}, [entryId]);

	// Handle edit button click
	const handleEdit = () => {
		if (entry) {
			const dateString = new Date(entry.date).toISOString().split('T')[0];
			router.push(`/diary/write?date=${dateString}`);
		}
	};

	// Handle delete
	const handleDelete = async () => {
		try {
			setDeleting(true);
			await deleteDiaryEntry(entryId);
			toast.success('Entry deleted successfully');
			router.push('/diary');
		} catch (error) {
			console.error('Error deleting entry:', error);
			toast.error('Failed to delete entry');
		} finally {
			setDeleting(false);
			setShowDeleteConfirm(false);
		}
	};

	// Handle back navigation
	const handleBack = () => {
		router.push('/diary');
	};

	// Format date for display
	const formatDate = (dateString) => {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			weekday: 'long',
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	// Format time for display
	const formatTime = (dateString) => {
		const date = new Date(dateString);
		return date.toLocaleTimeString('en-US', {
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	if (loading) {
		return (
			<div className='flex flex-col min-h-screen'>
				<FixedHeader
					title='Entry Details'
					toggleSidebar={toggleSidebar}
				/>
				<div className='flex-1 flex items-center justify-center'>
					<div className='text-center'>
						<div className='animate-pulse'>
							<div className='w-12 h-12 bg-[#00A3A3] rounded-full mx-auto mb-4'></div>
							<p className='text-white text-lg'>Loading entry...</p>
						</div>
					</div>
				</div>
				<Sidebar
					isOpen={isSidebarOpen}
					onToggle={toggleSidebar}
				/>
			</div>
		);
	}

	if (error || !entry) {
		return (
			<div className='flex flex-col min-h-screen'>
				<FixedHeader
					title='Entry Details'
					toggleSidebar={toggleSidebar}
				/>
				<div className='flex-1 flex items-center justify-center'>
					<div className='text-center bg-red-400/10 rounded-2xl p-8 border border-red-400/20'>
						<div className='text-6xl mb-4'>📔</div>
						<p className='text-red-400 text-lg mb-4'>
							{error || 'Entry not found'}
						</p>
						<button
							onClick={handleBack}
							className='bg-[#00A3A3] hover:bg-[#008080] text-white px-6 py-2 rounded-lg transition-colors'>
							Go Back
						</button>
					</div>
				</div>
				<Sidebar
					isOpen={isSidebarOpen}
					onToggle={toggleSidebar}
				/>
			</div>
		);
	}

	return (
		<div className='flex flex-col min-h-screen'>
			<FixedHeader
				title={entry.title}
				toggleSidebar={toggleSidebar}
			/>

			<div className='flex-1 overflow-hidden'>
				<div className='max-w-4xl mx-auto px-4 py-6'>
					{/* Back Button */}
					{/* <button
						onClick={handleBack}
						className='flex items-center space-x-2 text-white/70 hover:text-white 
								transition-colors mb-6 group'>
						<FaArrowLeft className='group-hover:-translate-x-1 transition-transform' />
						<span>Back to Diary</span>
					</button> */}

					{/* Entry Container */}
					<div className='bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden'>
						{/* Entry Header */}
						<div className='p-6 pb-4 border-b border-white/10'>
							<h1 className='text-white text-3xl font-bold mb-4 leading-tight'>
								{entry.title}
							</h1>
							<div className='flex flex-wrap items-center gap-4 text-white/70 text-sm'>
								<div className='flex items-center space-x-2'>
									<FaCalendar className='text-[#00A3A3]' />
									<span>{formatDate(entry.date)}</span>
								</div>
								{entry.createdAt && (
									<div className='flex items-center space-x-2'>
										<span className='w-1 h-1 bg-white/40 rounded-full'></span>
										<span>{formatTime(entry.createdAt)}</span>
									</div>
								)}
							</div>
						</div>

						{/* Action Buttons */}
						<div className='p-6 pb-4 border-b border-white/10'>
							<div className='flex flex-wrap gap-3'>
								<button
									onClick={handleEdit}
									className='flex items-center space-x-2 bg-[#00A3A3] hover:bg-[#008080] 
											text-white px-6 py-3 rounded-xl transition-all duration-300 
											transform hover:scale-105 font-medium'>
									<FaEdit />
									<span>Edit Entry</span>
								</button>
								<button
									onClick={() => setShowDeleteConfirm(true)}
									className='flex items-center space-x-2 bg-red-600/20 hover:bg-red-600/30 
											text-red-400 hover:text-red-300 px-6 py-3 rounded-xl 
											transition-all duration-300 border border-red-500/20 font-medium'>
									<FaTrash />
									<span>Delete Entry</span>
								</button>
							</div>
						</div>

						{/* Entry Image */}
						{entry.image && (
							<div className='md:p-6 pb-4'>
								<div className='relative rounded-xl overflow-hidden bg-gray-800/50'>
									<img
										src={entry.image}
										alt={entry.title}
										className='w-full max-h-96 object-cover'
									/>
									<div
										className='absolute top-4 right-4 bg-black/50 backdrop-blur-sm 
											rounded-lg p-2'>
										<FaImage className='text-white/80' />
									</div>
								</div>
							</div>
						)}

						{/* Entry Content */}
						<div className='md:p-6'>
							<div className='bg-white/5 rounded-xl p-6 border border-white/10'>
								<h3 className='text-white text-lg font-semibold mb-4 flex items-center space-x-2'>
									<span className='w-2 h-2 bg-[#00A3A3] rounded-full'></span>
									<span>Story</span>
								</h3>
								<div className='text-white/90 leading-relaxed text-base'>
									<p className='whitespace-pre-wrap'>{entry.story}</p>
								</div>
							</div>
						</div>

						{/* Entry Footer */}
						<div className='p-6 pt-0'>
							<div className='text-center text-white/40 text-sm'>
								{entry.updatedAt && entry.updatedAt !== entry.createdAt && (
									<p>
										Last updated: {formatDate(entry.updatedAt)} at{' '}
										{formatTime(entry.updatedAt)}
									</p>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Delete Confirmation Modal */}
			{showDeleteConfirm && (
				<div className='fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
					<div
						className='bg-gray-900/90 backdrop-blur-md rounded-2xl p-6 max-w-md w-full 
							border border-white/10 shadow-2xl'>
						<div className='text-center mb-6'>
							<div className='w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4'>
								<FaTrash className='text-red-400 text-xl' />
							</div>
							<h3 className='text-white text-xl font-semibold mb-2'>
								Delete Entry
							</h3>
							<p className='text-white/70 text-sm leading-relaxed'>
								Are you sure you want to delete "{entry.title}"? This action
								cannot be undone and all content will be permanently lost.
							</p>
						</div>
						<div className='flex space-x-3'>
							<button
								onClick={() => setShowDeleteConfirm(false)}
								className='flex-1 bg-white/10 hover:bg-white/20 text-white 
										py-3 px-4 rounded-xl transition-all duration-300 font-medium
										border border-white/20'>
								Cancel
							</button>
							<button
								onClick={handleDelete}
								disabled={deleting}
								className='flex-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 
										text-white py-3 px-4 rounded-xl transition-all duration-300
										disabled:cursor-not-allowed font-medium disabled:opacity-60'>
								{deleting ? (
									<span className='flex items-center justify-center space-x-2'>
										<div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
										<span>Deleting...</span>
									</span>
								) : (
									'Delete'
								)}
							</button>
						</div>
					</div>
				</div>
			)}

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default DiaryEntryDetails;
