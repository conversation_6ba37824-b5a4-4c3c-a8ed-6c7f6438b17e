'use client';
import { diary } from '@/assets/images';
import { FixedHeader } from '@/src/components/layout/FixedHeader';
import { Sidebar } from '@/src/components/layout/Sidebar';
import { Calendar } from '@/src/components/ui/calendar';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { diaryService } from '@/src/lib/api';
import { format } from 'date-fns';

const Diary = () => {
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [date, setDate] = useState(new Date());
	const [recentEntries, setRecentEntries] = useState([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const router = useRouter();

	const toggleSidebar = () => {
		setIsSidebarOpen(!isSidebarOpen);
	};

	// Fetch recent entries on component mount
	useEffect(() => {
		const fetchRecentEntries = async () => {
			try {
				setLoading(true);
				const response = await diaryService.getRecentEntries();
				setRecentEntries(response.data.entries || []);
			} catch (err) {
				console.error('Error fetching recent entries:', err);
				setError('Failed to load recent entries');
			} finally {
				setLoading(false);
			}
		};

		fetchRecentEntries();
	}, []);

	// Handle calendar date selection
	const handleDateSelect = (selectedDate) => {
		setDate(selectedDate);

		const dateString = format(selectedDate, 'yyyy-MM-dd');
		router.push(`/diary/write?date=${dateString}`);
	};

	// Handle entry click
	const handleEntryClick = (entry) => {
		router.push(`/diary/${entry._id}`);
	};

	// Format date for display
	const formatDate = (dateString) => {
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			day: '2-digit',
			month: '2-digit',
			year: 'numeric',
		});
	};

	return (
		<div className='flex flex-col min-h-screen'>
			<FixedHeader
				title='My Diary'
				toggleSidebar={toggleSidebar}
			/>

			{/* Main Content Container */}
			<div className='flex-1 overflow-hidden'>
				<div className='max-w-6xl mx-auto px-4 py-6'>
					{/* Calendar and Quick Stats Section */}
					<div className='grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8'>
						{/* Calendar Card */}
						<div className='lg:col-span-2'>
							<div className='bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10'>
								<h3 className='text-white text-xl font-semibold mb-4'>
									Select a Date
								</h3>
								<div className='flex justify-center'>
									<Calendar
										mode='single'
										selected={date}
										onSelect={handleDateSelect}
										className='rounded-xl bg-[#D9D9D9] text-black shadow-lg'
										modifiers={{
											today: new Date(),
										}}
										modifiersStyles={{
											today: {
												backgroundColor: '#008080',
												color: 'white',
												fontWeight: 'bold',
												borderRadius: '8px',
											},
										}}
									/>
								</div>
							</div>
						</div>

						{/* Quick Stats Card */}
						<div className='space-y-4'>
							<div className='bg-[#00A3A3]/10 backdrop-blur-sm rounded-2xl p-6 border border-[#00A3A3]/20'>
								<div className='text-center'>
									<div className='text-3xl font-bold text-[#00A3A3] mb-2'>
										{recentEntries.length}
									</div>
									<div className='text-white/70 text-sm'>Total Entries</div>
								</div>
							</div>

							<div className='bg-[#008080]/10 backdrop-blur-sm rounded-2xl p-6 border border-[#008080]/20'>
								<div className='text-center'>
									<div className='text-3xl font-bold text-[#008080] mb-2'>
										{
											recentEntries.filter((entry) => {
												const entryDate = new Date(entry.date);
												const today = new Date();
												const diffTime = Math.abs(today - entryDate);
												const diffDays = Math.ceil(
													diffTime / (1000 * 60 * 60 * 24),
												);
												return diffDays <= 7;
											}).length
										}
									</div>
									<div className='text-white/70 text-sm'>This Week</div>
								</div>
							</div>
						</div>
					</div>

					{/* Recent Entries Section */}
					<div className='bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10'>
						{/* Section Header */}
						<div className='flex items-center justify-between mb-6'>
							<h2 className='text-white text-2xl font-semibold'>
								Recent Entries
							</h2>
						</div>

						{/* Loading State */}
						{loading && (
							<div className='text-white text-center py-12'>
								<div className='animate-pulse'>
									<div className='w-8 h-8 bg-[#00A3A3] rounded-full mx-auto mb-4'></div>
									<div>Loading entries...</div>
								</div>
							</div>
						)}

						{/* Error State */}
						{error && (
							<div className='text-red-400 text-center py-12 bg-red-400/10 rounded-xl border border-red-400/20'>
								{error}
							</div>
						)}

						{/* Entries Grid */}
						{!loading && !error && (
							<div className='space-y-4'>
								{recentEntries.length > 0 ? (
									<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
										{recentEntries.map((entry) => (
											<div
												key={entry._id}
												onClick={() => handleEntryClick(entry)}
												className='group bg-[#00A3A3] rounded-xl p-5 cursor-pointer
												hover:bg-[#008080] transition-all duration-300 transform hover:scale-105
												hover:shadow-lg border border-[#00A3A3]/20'>
												<div className='text-white/80 text-sm mb-2 font-medium'>
													{formatDate(entry.date)}
												</div>
												<div className='text-white text-lg font-semibold group-hover:text-white/90 transition-colors'>
													{entry.title}
												</div>
												<div className='mt-3 text-white/60 text-sm'>
													Click to read →
												</div>
											</div>
										))}
									</div>
								) : (
									<div className='text-center py-16'>
										<div className='text-6xl mb-4'>📔</div>
										<div className='text-white/70 text-lg mb-2'>
											No diary entries yet
										</div>
										<div className='text-white/50 text-sm'>
											Start writing your story and capture your memories!
										</div>
									</div>
								)}
							</div>
						)}
					</div>
				</div>
			</div>

			{/* Floating Add Button */}
			<div className='fixed bottom-6 right-6 z-40'>
				<Link
					href='/diary/write'
					className='bg-[#008080] hover:bg-[#008080]/90 text-white px-6 py-3 rounded-full 
					font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg
					flex items-center gap-2 backdrop-blur-sm border border-[#008080]/20'>
					<span className='text-lg'>+</span>
					<span>New Entry</span>
				</Link>
			</div>

			<Sidebar
				isOpen={isSidebarOpen}
				onToggle={toggleSidebar}
			/>
		</div>
	);
};

export default Diary;
