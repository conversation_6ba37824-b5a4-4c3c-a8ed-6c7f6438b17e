'use client';
import React, {
	useState,
	useEffect,
	useRef,
	useCallback,
	Suspense,
} from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { FaArrowLeft } from 'react-icons/fa';
import { authAPI } from '@/api/auth';

// Loading component for Suspense fallback
const LoadingSpinner = () => (
	<div className='min-h-screen flex flex-col items-center justify-center px-6'>
		<div className='bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20 text-center'>
			<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4'></div>
			<p className='text-white text-lg'>Loading...</p>
		</div>
	</div>
);

// Main component that uses useSearchParams
const VerifyPasswordResetOTPContent = () => {
	const router = useRouter();
	const searchParams = useSearchParams();
	const email = searchParams.get('email');

	const [otp, setOtp] = useState(['', '', '', '', '', '']);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState('');
	const [isResending, setIsResending] = useState(false);
	const [resendTimer, setResendTimer] = useState(0);
	const inputRefs = useRef([]);

	// Timer for resend functionality
	useEffect(() => {
		let interval;
		if (resendTimer > 0) {
			interval = setInterval(() => {
				setResendTimer((prev) => prev - 1);
			}, 1000);
		}
		return () => clearInterval(interval);
	}, [resendTimer]);

	const handlePaste = useCallback(
		(e) => {
			e.preventDefault();
			const pastedData = e.clipboardData.getData('text/plain').trim();

			// Check if pasted content is a valid OTP
			if (pastedData.length === 6 && /^\d+$/.test(pastedData)) {
				const newOtp = pastedData.split('');
				setOtp(newOtp);

				// Clear error when pasting
				if (error) setError('');

				// Focus the last input after paste
				if (inputRefs.current[5]) {
					inputRefs.current[5].focus();
				}
			}
		},
		[error],
	);

	const handleOtpChange = (index, value) => {
		// Only allow numbers
		if (!/^\d*$/.test(value)) return;

		const newOtp = [...otp];
		newOtp[index] = value;
		setOtp(newOtp);

		// Clear error when typing
		if (error) setError('');

		// Auto-focus next input
		if (value && index < 5) {
			if (inputRefs.current[index + 1]) {
				inputRefs.current[index + 1].focus();
			}
		}
	};

	const handleKeyDown = (index, e) => {
		// Handle backspace
		if (e.key === 'Backspace' && !otp[index] && index > 0) {
			if (inputRefs.current[index - 1]) {
				inputRefs.current[index - 1].focus();
			}
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();

		const otpCode = otp.join('');
		if (otpCode.length !== 6) {
			setError('Please enter the complete 6-digit code');
			return;
		}

		setIsLoading(true);
		setError('');

		try {
			const response = await authAPI.verifyPasswordResetOTP({
				email,
				otp: otpCode,
			});

			// Redirect to reset password with token
			router.push(
				`/auth/reset-password?email=${encodeURIComponent(email)}&token=${
					response.token || otpCode
				}`,
			);
		} catch (error) {
			console.error('OTP verification error:', error);
			setError(
				error.response?.data?.message ||
					error.message ||
					'Invalid or expired code. Please try again.',
			);
			// Clear OTP fields on error
			setOtp(['', '', '', '', '', '']);
			if (inputRefs.current[0]) {
				inputRefs.current[0].focus();
			}
		} finally {
			setIsLoading(false);
		}
	};

	const handleResendOTP = async () => {
		if (resendTimer > 0) return;

		setIsResending(true);
		setError('');

		try {
			await authAPI.resendPasswordResetOTP({ email });
			setResendTimer(60); // 60 seconds cooldown
			setOtp(['', '', '', '', '', '']); // Clear current OTP
			// Focus first input after resend
			if (inputRefs.current[0]) {
				inputRefs.current[0].focus();
			}
		} catch (error) {
			console.error('Resend OTP error:', error);
			setError(
				error.response?.data?.message ||
					error.message ||
					'Failed to resend code. Please try again.',
			);
		} finally {
			setIsResending(false);
		}
	};

	// Redirect if no email
	if (!email) {
		return (
			<div className='min-h-screen flex flex-col items-center justify-center px-6'>
				<div className='bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20 text-center'>
					<p className='text-red-300 mb-6 text-lg'>
						No email provided for password reset.
					</p>
					<Link
						href='/auth/forgot-password'
						className='bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-200'>
						Back to Forgot Password
					</Link>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen flex flex-col'>
			{/* Header with back button */}
			<div className='flex items-center px-6 py-8 mb-8'>
				<Link
					href='/auth/forgot-password'
					className='rounded-full bg-white/10 backdrop-blur-sm border border-white/20 p-3 mr-4 hover:bg-white/20 transition-all duration-200'>
					<FaArrowLeft className='text-white text-lg' />
				</Link>
				<h2 className='text-2xl font-bold text-white'>Verify Code</h2>
			</div>

			{/* Main content container */}
			<div className='flex-1 flex flex-col justify-center px-6 pb-8'>
				{/* Card container */}
				<div className='bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20'>
					{/* Title section */}
					<div className='text-center mb-8'>
						<h3 className='text-3xl font-bold text-white mb-4'>
							Enter Verification Code
						</h3>
						<p className='text-white/80 text-lg leading-relaxed mb-2'>
							We've sent a 6-digit code to
						</p>
						<p className='text-teal-300 font-medium text-lg'>{email}</p>
					</div>

					{/* Form */}
					<form
						onSubmit={handleSubmit}
						className='space-y-6'
						onPaste={handlePaste}>
						{/* OTP Input Fields */}
						<div className='flex justify-center items-center gap-3 mb-8'>
							{otp.map((digit, index) => (
								<input
									key={index}
									type='text'
									name={`otp-${index}`}
									value={digit}
									onChange={(e) => handleOtpChange(index, e.target.value)}
									onKeyDown={(e) => handleKeyDown(index, e)}
									ref={(el) => (inputRefs.current[index] = el)}
									className='w-14 h-14 text-2xl text-center rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 text-white font-bold focus:outline-none focus:ring-2 focus:ring-teal-400 focus:border-transparent transition-all duration-200 placeholder-white/40'
									maxLength={1}
									inputMode='numeric'
								/>
							))}
						</div>

						{/* Error message */}
						{error && (
							<div className='bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-red-200 text-sm text-center backdrop-blur-sm'>
								{error}
							</div>
						)}

						{/* Resend OTP */}
						<div className='text-center mb-6'>
							<p className='text-white/80 text-sm mb-3'>
								Didn't receive the code?
							</p>
							<button
								type='button'
								onClick={handleResendOTP}
								disabled={isResending || resendTimer > 0}
								className={`font-semibold text-sm transition-all duration-200 ${
									isResending || resendTimer > 0
										? 'text-white/40 cursor-not-allowed'
										: 'text-teal-300 hover:text-teal-200 cursor-pointer'
								}`}>
								{isResending
									? 'Sending...'
									: resendTimer > 0
									? `Resend in ${resendTimer}s`
									: 'Resend Code'}
							</button>
						</div>

						{/* Submit button */}
						<button
							type='submit'
							disabled={isLoading || otp.join('').length !== 6}
							className='w-full p-4 rounded-2xl bg-teal-600 hover:bg-teal-700 text-white text-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-teal-600 shadow-lg'>
							{isLoading ? (
								<div className='flex items-center justify-center'>
									<div className='animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-2'></div>
									Verifying...
								</div>
							) : (
								'Verify Code'
							)}
						</button>
					</form>

					{/* Additional help text */}
					<div className='text-center mt-6'>
						<p className='text-white/60 text-sm'>Code expires in 10 minutes.</p>
					</div>
				</div>

				{/* Bottom spacing */}
				<div className='mt-8 text-center'>
					<Link
						href='/auth/forgot-password'
						className='text-white/60 hover:text-white transition-colors duration-200'>
						Try a different email
					</Link>
				</div>
			</div>
		</div>
	);
};

// Main component with Suspense wrapper
const VerifyPasswordResetOTP = () => {
	return (
		<Suspense fallback={<LoadingSpinner />}>
			<VerifyPasswordResetOTPContent />
		</Suspense>
	);
};

export default VerifyPasswordResetOTP;
