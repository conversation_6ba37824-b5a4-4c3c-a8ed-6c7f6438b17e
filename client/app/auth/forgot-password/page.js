'use client';
import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { FaArrowLeft } from 'react-icons/fa';
import { authAPI } from '../../../api/auth'; // Adjust path as needed
import { useRouter } from 'next/navigation';

const ForgotPassword = () => {
	const router = useRouter();
	const [email, setEmail] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState('');

	const handleEmailChange = (e) => {
		setEmail(e.target.value);
		if (error) setError(''); // Clear error when typing
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setIsLoading(true);
		setError('');

		try {
			await authService.forgotPassword({ email });
			console.log('Password reset code sent to:', email);
			// Navigate to verify OTP page with email as query param
			router.push(
				`/auth/forgot-password/verify-otp?email=${encodeURIComponent(email)}`,
			);
		} catch (error) {
			console.error('Forgot password error:', error);
			setError(
				error.response?.data?.message ||
					error.message ||
					'Failed to send reset code. Please try again.',
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className='min-h-screen flex flex-col'>
			{/* Header with back button */}
			<div className='flex items-center px-6 py-8 mb-8'>
				<Link
					href='/auth/login'
					className='rounded-full bg-white/10 backdrop-blur-sm border border-white/20 p-3 mr-4 hover:bg-white/20 transition-all duration-200'>
					<FaArrowLeft className='text-white text-lg' />
				</Link>
				<h2 className='text-2xl font-bold text-white'>Forgot Password</h2>
			</div>

			{/* Main content container */}
			<div className='flex-1 flex flex-col justify-center px-6 pb-8'>
				{/* Card container */}
				<div className='bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20'>
					{/* Title section */}
					<div className='text-center mb-8'>
						<h3 className='text-3xl font-bold text-white mb-4'>
							Reset Your Password
						</h3>
						<p className='text-white/80 text-lg leading-relaxed'>
							Enter your email address and we'll send you a verification code to
							reset your password
						</p>
					</div>

					{/* Form */}
					<form
						onSubmit={handleSubmit}
						className='space-y-6'>
						{/* Email input */}
						<div className='relative'>
							<input
								type='email'
								name='email'
								placeholder='Enter your email address'
								value={email}
								onChange={handleEmailChange}
								className='w-full p-4 rounded-2xl bg-white/20 backdrop-blur-sm border border-white/30 text-white placeholder-white/60 text-lg focus:outline-none focus:ring-2 focus:ring-teal-400 focus:border-transparent transition-all duration-200'
								required
							/>
						</div>

						{/* Error message */}
						{error && (
							<div className='bg-red-500/20 border border-red-500/30 rounded-xl p-4 text-red-200 text-sm text-center backdrop-blur-sm'>
								{error}
							</div>
						)}

						{/* Submit button */}
						<button
							type='submit'
							disabled={isLoading || !email}
							className='w-full p-4 rounded-2xl bg-teal-600 hover:bg-teal-700 text-white text-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-teal-600 shadow-lg'>
							{isLoading ? (
								<div className='flex items-center justify-center'>
									<div className='animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-2'></div>
									Sending Code...
								</div>
							) : (
								'Send Verification Code'
							)}
						</button>
					</form>
				</div>

				{/* Bottom spacing */}
				<div className='mt-8 text-center'>
					<Link
						href='/auth/login'
						className='text-white/60 hover:text-white transition-colors duration-200'>
						Remember your password? Sign in
					</Link>
				</div>
			</div>
		</div>
	);
};

export default ForgotPassword;
