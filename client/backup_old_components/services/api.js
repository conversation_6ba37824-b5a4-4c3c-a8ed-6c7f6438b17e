const API_BASE_URL =
	process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:5000/';

// Helper function to get auth token
const getAuthToken = () => {
	if (typeof window !== 'undefined') {
		return localStorage.getItem('accessToken');
	}
	return null;
};

// Helper function to create headers with auth
const createHeaders = () => {
	const token = getAuthToken();
	const headers = {
		'Content-Type': 'application/json',
	};

	if (token) {
		headers.Authorization = `Bearer ${token}`;
	}

	return headers;
};

// Generic API request function
const apiRequest = async (url, options = {}) => {
	try {
		const response = await fetch(`${API_BASE_URL}${url}`, {
			headers: createHeaders(),
			...options,
		});

		const data = await response.json();

		if (!response.ok) {
			throw new Error(data.message || 'Something went wrong');
		}

		return data;
	} catch (error) {
		console.error('API Request Error:', error);
		throw error;
	}
};

// Goals API
export const goalsAPI = {
	// Get user's goals
	getUserGoals: async (params = {}) => {
		const queryString = new URLSearchParams(params).toString();
		return apiRequest(`api/goals?${queryString}`);
	},

	// Get public goals
	getPublicGoals: async (params = {}) => {
		const queryString = new URLSearchParams(params).toString();
		return apiRequest(`api/goals/public?${queryString}`);
	},

	// Get goal by ID
	getGoalById: async (id) => {
		return apiRequest(`api/goals/${id}`);
	},

	// Create new goal
	createGoal: async (goalData) => {
		return apiRequest('api/goals', {
			method: 'POST',
			body: JSON.stringify(goalData),
		});
	},

	// Update goal
	updateGoal: async (id, goalData) => {
		return apiRequest(`api/goals/${id}`, {
			method: 'PUT',
			body: JSON.stringify(goalData),
		});
	},

	// Delete goal
	deleteGoal: async (id) => {
		return apiRequest(`api/goals/${id}`, {
			method: 'DELETE',
		});
	},

	// Add task to goal
	addTask: async (goalId, taskData) => {
		return apiRequest(`api/goals/${goalId}/tasks`, {
			method: 'POST',
			body: JSON.stringify(taskData),
		});
	},

	// Update task status
	updateTaskStatus: async (goalId, taskId, status) => {
		return apiRequest(`api/goals/${goalId}/tasks/${taskId}`, {
			method: 'PUT',
			body: JSON.stringify({ status }),
		});
	},

	// Delete task
	deleteTask: async (goalId, taskId) => {
		return apiRequest(`api/goals/${goalId}/tasks/${taskId}`, {
			method: 'DELETE',
		});
	},
};

// Values API
export const valuesAPI = {
	// Get user's values
	getUserValues: async (params = {}) => {
		const queryString = new URLSearchParams(params).toString();
		return apiRequest(`api/values?${queryString}`);
	},

	// Get public values
	getPublicValues: async (params = {}) => {
		const queryString = new URLSearchParams(params).toString();
		return apiRequest(`api/values/public?${queryString}`);
	},

	// Get value by ID
	getValueById: async (id) => {
		return apiRequest(`api/values/${id}`);
	},

	// Create new value
	createValue: async (valueData) => {
		return apiRequest('api/values', {
			method: 'POST',
			body: JSON.stringify(valueData),
		});
	},

	// Update value
	updateValue: async (id, valueData) => {
		return apiRequest(`api/values/${id}`, {
			method: 'PUT',
			body: JSON.stringify(valueData),
		});
	},

	// Delete value
	deleteValue: async (id) => {
		return apiRequest(`api/values/${id}`, {
			method: 'DELETE',
		});
	},
};

export default {
	goals: goalsAPI,
	values: valuesAPI,
};
