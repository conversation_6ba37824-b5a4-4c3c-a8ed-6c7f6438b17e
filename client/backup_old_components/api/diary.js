import { postData, getData, putData, deleteData } from './index';

// Create a new diary entry
export const createDiaryEntry = async (entryData) => {
	try {
		const response = await postData('/api/diary', entryData);
		return response.data;
	} catch (error) {
		console.error('Create diary entry error:', error);
		throw error;
	}
};

// Get all diary entries for user
export const getDiaryEntries = async (params = {}) => {
	try {
		const queryString = new URLSearchParams(params).toString();
		const url = queryString ? `/api/diary?${queryString}` : '/api/diary';
		const response = await getData(url);
		return response.data;
	} catch (error) {
		console.error('Get diary entries error:', error);
		throw error;
	}
};

// Get recent diary entries
export const getRecentDiaryEntries = async () => {
	try {
		const response = await getData('/api/diary/recent');
		return response.data;
	} catch (error) {
		console.error('Get recent diary entries error:', error);
		if (error.response) {
			console.error('Error response:', error.response.data);
			console.error('Error status:', error.response.status);
		}
		throw error;
	}
};

// Get diary entry by ID
export const getDiaryEntryById = async (id) => {
	try {
		const response = await getData(`/api/diary/${id}`);
		return response.data;
	} catch (error) {
		console.error('Get diary entry by ID error:', error);
		throw error;
	}
};

// Get diary entry by date
export const getDiaryEntryByDate = async (date) => {
	try {
		const response = await getData(`/api/diary/date/${date}`);
		return response.data;
	} catch (error) {
		// Return null if no entry found for this date (404)
		if (error.response?.status === 404) {
			return null;
		}
		console.error('Get diary entry by date error:', error);
		throw error;
	}
};

// Update diary entry
export const updateDiaryEntry = async (id, entryData) => {
	try {
		const response = await putData(`/api/diary/${id}`, entryData);
		return response.data;
	} catch (error) {
		console.error('Update diary entry error:', error);
		throw error;
	}
};

// Delete diary entry
export const deleteDiaryEntry = async (id) => {
	try {
		const response = await deleteData(`/api/diary/${id}`);
		return response.data;
	} catch (error) {
		console.error('Delete diary entry error:', error);
		throw error;
	}
};

// Upload image to Cloudinary
export const uploadImage = async (file) => {
	try {
		const formData = new FormData();
		formData.append('image', file);

		const response = await postData('/api/diary/upload-image', formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		});
		return response.data;
	} catch (error) {
		console.error('Upload image error:', error);
		throw error;
	}
};
