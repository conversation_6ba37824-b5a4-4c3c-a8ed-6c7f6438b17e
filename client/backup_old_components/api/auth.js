import { postData } from './index';

// Authentication API endpoints
export const authAPI = {
	// Register user
	register: async (data) => {
		const response = await postData('/api/auth/register', data);
		return response.data;
	},

	// Verify email with OTP
	verifyEmail: async (data) => {
		const response = await postData('/api/auth/verify-email', data);
		return response.data;
	},

	// Login user
	login: async (data) => {
		const response = await postData('/api/auth/login', data);
		return response.data;
	},

	// Forgot password
	forgotPassword: async (data) => {
		const response = await postData('/api/auth/forgot-password', data);
		return response.data;
	},

	// Verify password reset OTP
	verifyPasswordResetOTP: async (data) => {
		const response = await postData(
			'/api/auth/verify-password-reset-otp',
			data,
		);
		return response.data;
	},

	// Reset password
	resetPassword: async (data) => {
		const response = await postData('/api/auth/reset-password', data);
		return response.data;
	},

	// Resend password reset OTP
	resendPasswordResetOTP: async (data) => {
		const response = await postData('/api/auth/forgot-password', data);
		return response.data;
	},

	// Google signin
	googleLogin: async (data) => {
		const response = await postData('/api/auth/google-login', data);
		return response.data;
	},
};
