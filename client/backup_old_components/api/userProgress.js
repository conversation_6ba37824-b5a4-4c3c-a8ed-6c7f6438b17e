import { postData, getData, deleteData } from './index';

export const userProgressAPI = {
	// Update user progress (video progress, reflections, notes)
	updateProgress: async (progressData) => {
		const response = await postData('/api/user/progress', progressData);
		return response.data;
	},

	// Get user progress for a specific lesson
	getProgress: async (lessonId) => {
		const response = await getData(`/api/user/progress/${lessonId}`);
		return response.data;
	},

	// Get all user progress (for ongoing videos)
	getAllProgress: async () => {
		const response = await getData(`/api/user/progress/all`);
		return response.data;
	},

	// Update reflection answer
	updateReflection: async (reflectionData) => {
		const response = await postData('/api/user/reflection', reflectionData);
		return response.data;
	},

	// Delete reflection answer
	deleteReflection: async (lessonId, questionIndex) => {
		const response = await deleteData(
			`/api/user/reflection/${lessonId}/${questionIndex}`,
		);
		return response.data;
	},
};
