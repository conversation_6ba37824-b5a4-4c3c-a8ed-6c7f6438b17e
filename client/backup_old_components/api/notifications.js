import { postData } from './index';

export const notificationAPI = {
  // Subscribe to notifications
  subscribe: (subscriptionData) => {
    return postData('/api/notification/subscribe', subscriptionData);
  },

  // Send notification (admin only)
  sendNotification: (notificationData) => {
    return postData('/api/notification/send-notification', notificationData);
  },

  // Broadcast notification to all subscribers (admin only)
  broadcastNotification: (notificationData) => {
    return postData('/api/notification/broadcast', notificationData);
  },

  // Unsubscribe from notifications
  unsubscribe: (unsubscribeData) => {
    return postData('/api/notification/unsubscribe', unsubscribeData);
  },
};
