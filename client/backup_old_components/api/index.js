import axios from 'axios';

const controller = new AbortController();

const baseURL = process?.env?.NEXT_PUBLIC_BASE_URL || 'http://localhost:5000';

const api = axios.create({
	baseURL,
	signal: controller.signal,
});

// Add interceptor to handle token expiration
api.interceptors.response.use(
	(response) => response,
	(error) => {
		if (error.response?.status === 401) {
			window.localStorage.removeItem('accessToken');
			window.localStorage.removeItem('refreshToken');
			window.dispatchEvent(new Event('auth-change'));
			window.location.href = '/auth/login';
		}
		return Promise.reject(error);
	}
);

const setHeaderAuthorization = (token) => {
	if (token) {
		api.defaults.headers.common.Authorization = `Bearer ${token}`;
	} else {
		delete api.defaults.headers.common.Authorization;
	}
};

const postData = (url, data, options) => {
	return api.post(url, data, options);
};

const getData = (url, options) => {
	return api.get(url, options);
};

const putData = (url, data, options) => {
	return api.put(url, data, options);
};

const patchData = (url, data, options) => {
	return api.patch(url, data, options);
};

const deleteData = (url, options) => {
	return api.delete(url, options);
};

const abortOutgoingRequest = () => {
	controller.abort();
};

// Initialize token from localStorage on app startup
if (typeof window !== 'undefined') {
	const storedToken = localStorage.getItem('accessToken');
	if (storedToken) {
		setHeaderAuthorization(storedToken);
	}
}

export {
	api as default,
	setHeaderAuthorization,
	postData,
	getData,
	putData,
	patchData,
	deleteData,
	abortOutgoingRequest,
};
