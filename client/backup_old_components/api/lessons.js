import { getData, postData, putData, deleteData } from './index';

export const lessonsAPI = {
	// Get all lessons with pagination and filters
	getLessons: (params = {}) => {
		const queryParams = new URLSearchParams();

		if (params.page) queryParams.append('page', params.page);
		if (params.limit) queryParams.append('limit', params.limit);
		if (params.category) queryParams.append('category', params.category);
		if (params.title) queryParams.append('title', params.title);

		const queryString = queryParams.toString();
		const url = queryString
			? `/api/lesson/lessons?${queryString}`
			: '/api/lesson/lessons';

		return getData(url);
	},

	// Get single lesson by ID
	getLessonById: (id) => {
		return getData(`/api/lesson/${id}`);
	},

	// Upload new lesson (JSON data with URLs)
	uploadLesson: (lessonData, options = {}) => {
		return postData('/api/lesson/lessons', lessonData, {
			headers: {
				'Content-Type': 'application/json',
			},
			...options,
		});
	},

	// Update lesson (JSON data with URLs)
	updateLesson: (id, lessonData) => {
		return putData(`/api/lesson/${id}`, lessonData, {
			headers: {
				'Content-Type': 'application/json',
			},
		});
	},

	// Delete lesson
	deleteLesson: (id) => {
		return deleteData(`/api/lesson/${id}`);
	},

	// Optional: Static categories (for now)
	getCategories: () => {
		return ['YOU', '=', 'LIFE'];
	},
};
