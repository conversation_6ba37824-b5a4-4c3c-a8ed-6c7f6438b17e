import { getData, putData, patchData, deleteData } from './index';

// User API endpoints
export const userAPI = {
	// Get user profile
	getProfile: async () => {
		const response = await getData('/api/user/profile');
		return response.data;
	},

	// Update user profile (for future use)
	updateProfile: async (data) => {
		const response = await putData('/api/user/profile', data);
		return response.data;
	},

	getAllUsers: async ({ page = 1, limit = 10, search = '' } = {}) => {
		const params = new URLSearchParams({
			page: page.toString(),
			limit: limit.toString(),
		});
		
		if (search) {
			params.append('search', search);
		}
		
		const response = await getData(`/api/user/allUser?${params.toString()}`);
		console.log(response.data);
		return response.data;
	},

	// Update user status
	updateUserStatus: async (userId, status) => {
		const response = await patchData(`/api/user/${userId}/status`, { status });
		return response.data;
	},

	// Delete user
	deleteUser: async (userId) => {
		const response = await deleteData(`/api/user/${userId}`);
		return response.data;
	},
};
