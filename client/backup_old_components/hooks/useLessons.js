import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { lessonsAPI } from '../api/lessons';
import { useUserProfile } from './useUser';

// Hook to get lessons with pagination and filters
export const useLessons = (params = {}) => {
	return useQuery({
		queryKey: ['lessons', params],
		queryFn: () => lessonsAPI.getLessons(params),
		staleTime: 5 * 60 * 1000, // 5 minutes
		cacheTime: 10 * 60 * 1000, // 10 minutes
		keepPreviousData: true, // Keep previous data while loading new data
	});
};

// Hook to get single lesson by ID
export const useLesson = (id) => {
	return useQuery({
		queryKey: ['lesson', id],
		queryFn: () => lessonsAPI.getLessonById(id),
		enabled: !!id, // Only fetch if id exists
		staleTime: 10 * 60 * 1000, // 10 minutes
	});
};

// Hook to upload new lesson with URLs
export const useUploadLesson = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (lessonData) => {
			try {
				const response = await lessonsAPI.uploadLesson(lessonData);
				return response;
			} catch (error) {
				// Re-throw the original error
				throw error;
			}
		},
		retry: (failureCount, error) => {
			// Don't retry on client errors (4xx) or specific server errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}

			// Don't retry on timeout errors
			if (error?.message?.includes('timeout') || error?.name === 'AbortError') {
				return false;
			}

			// Retry up to 2 times for other errors
			return failureCount < 2;
		},
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
		onSuccess: () => {
			// Invalidate lessons queries to refetch data
			queryClient.invalidateQueries({ queryKey: ['lessons'] });
		},
		onError: (error) => {
			console.error('Error uploading lesson:', error);

			// You can add toast notifications here if you have a toast system
			// Example: toast.error(error.message || 'Failed to upload lesson');
		},
	});
};

// Hook to update lesson
export const useUpdateLesson = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ id, data }) => {
			// Send lesson data with URLs
			return lessonsAPI.updateLesson(id, data);
		},
		retry: (failureCount, error) => {
			// Don't retry on client errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}
			return failureCount < 2;
		},
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
		onSuccess: (data, variables) => {
			// Invalidate lessons queries
			queryClient.invalidateQueries({ queryKey: ['lessons'] });
			// Update specific lesson cache
			queryClient.invalidateQueries({ queryKey: ['lesson', variables.id] });
		},
		onError: (error) => {
			console.error('Error updating lesson:', error);
		},
	});
};

// Hook to delete lesson
export const useDeleteLesson = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: lessonsAPI.deleteLesson,
		retry: (failureCount, error) => {
			// Don't retry on client errors
			if (error?.response?.status >= 400 && error?.response?.status < 500) {
				return false;
			}
			return failureCount < 2;
		},
		onSuccess: () => {
			// Invalidate lessons queries to refetch data
			queryClient.invalidateQueries({ queryKey: ['lessons'] });
		},
		onError: (error) => {
			console.error('Error deleting lesson:', error);
		},
	});
};

// Hook to check if user can manage lessons (create, edit, delete)
export const useCanManageLessons = () => {
	const { data: userProfile } = useUserProfile();

	if (!userProfile?.data?.user?.roles) return false;

	const userRoles = userProfile.data.user.roles;
	return userRoles.includes('superadmin') || userRoles.includes('tutor');
};

// Hook to get lesson categories
export const useLessonCategories = () => {
	return {
		data: lessonsAPI.getCategories(),
		isLoading: false,
		error: null,
	};
};
