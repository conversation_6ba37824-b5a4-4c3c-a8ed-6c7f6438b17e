'use client';
import { logo } from '@/assets/images';
import { siteName } from '@/utils/variables';
import Image from 'next/image';
import React from 'react';

const SplashScreen = () => {
	return (
		<div className='min-h-screen flex flex-col items-center justify-center bg-gray-50 relative overflow-hidden'>
			{/* Background circles */}
			<div className='absolute top-0 right-0 w-96 h-96 bg-gray-100/50 rounded-full -mr-36 -mt-36 z-0 animate-float'></div>

			<div className='absolute -mt-28 z-0 animate-pulse-custom'></div>

			<div className='absolute bottom-0 left-0 w-80 h-80 bg-gray-100/95 rounded-full -ml-24 -mb-24 z-0 animate-sway'></div>

			{/* Centered animated circle */}
			<div className='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gray-200/50 rounded-full z-0 animate-breathe' />

			{/* Main content */}
			<div className='relative z-10 p-20 md:p-24 lg:p-32 transition-all duration-300'>
				<div className='text-center'>
					<Image
						src={logo}
						alt={siteName}
						width={180}
						height={180}
						className='mx-auto'
					/>
				</div>
			</div>

			{/* Custom animation keyframes */}
			<style jsx>{`
				@keyframes float {
					0% {
						transform: translate(0, 0);
					}
					50% {
						transform: translate(-15px, 15px);
					}
					100% {
						transform: translate(0, 0);
					}
				}

				@keyframes pulse-custom {
					0%,
					100% {
						opacity: 0.5;
						transform: scale(1);
					}
					50% {
						opacity: 0.7;
						transform: scale(1.05);
					}
				}

				@keyframes sway {
					0% {
						transform: translateX(0);
					}
					50% {
						transform: translateX(20px);
					}
					100% {
						transform: translateX(0);
					}
				}

				@keyframes breathe {
					0%,
					100% {
						transform: scale(1);
					}
					50% {
						transform: scale(1.1);
					}
				}

				.animate-float {
					animation: float 15s ease-in-out infinite alternate;
				}

				.animate-pulse-custom {
					animation: pulse-custom 8s ease-in-out infinite;
				}

				.animate-sway {
					animation: sway 12s ease-in-out infinite;
				}

				.animate-breathe {
					animation: breathe 10s ease-in-out infinite;
				}
			`}</style>
		</div>
	);
};

export default SplashScreen;
