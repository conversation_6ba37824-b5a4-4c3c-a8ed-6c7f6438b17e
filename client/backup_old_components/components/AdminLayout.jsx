import React from 'react';
import AdminNavbar from './AdminNavbar';
import AdminSidebar from './AdminSidebar';
import AdminProtectedRoute from './AdminProtectedRoute';

const AdminLayout = ({ children }) => {
	return (
		<AdminProtectedRoute>
			<div className='bg-gray-100 h-screen flex flex-col'>
				<AdminNavbar />

				<div className='flex flex-1 overflow-hidden'>
					<AdminSidebar />
					<main className='flex-grow overflow-y-auto p-6'>{children}</main>
				</div>
			</div>
		</AdminProtectedRoute>
	);
};

export default AdminLayout;
