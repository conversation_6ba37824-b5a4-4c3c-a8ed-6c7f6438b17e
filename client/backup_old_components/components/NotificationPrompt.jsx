'use client';
import { useState, useEffect } from 'react';
import { FaBell, FaTimes, FaCheck } from 'react-icons/fa';
import {
	useSubscribeToNotifications,
	useUnsubscribeFromNotifications,
	isNotificationSupported,
	isNotificationGranted,
	checkExistingSubscription,
} from '@/hooks/useNotifications';
import { useUserProfile } from '@/hooks/useUser';
import { isAuthenticated } from '@/hooks/useAuth';
import toast from 'react-hot-toast';

const NotificationPrompt = () => {
	const [showPrompt, setShowPrompt] = useState(false);
	const [isSubscribed, setIsSubscribed] = useState(false);
	const [hasCheckedSubscription, setHasCheckedSubscription] = useState(false);

	const { data: userProfile } = useUserProfile();
	const userData = userProfile?.user;
	// console.log('Userdata', userData);

	const subscribeNotificationMutation = useSubscribeToNotifications(userData);
	const unsubscribeNotificationMutation =
		useUnsubscribeFromNotifications(userData);

	// Check if user should see the prompt
	useEffect(() => {
		const checkNotificationStatus = async () => {
			// Only show for authenticated users
			if (!isAuthenticated()) {
				setHasCheckedSubscription(true);
				return;
			}

			// Check if notifications are supported
			if (!isNotificationSupported()) {
				setHasCheckedSubscription(true);
				return;
			}

			// Check if user has already dismissed the prompt permanently
			const hasDeclinedPermanently =
				localStorage.getItem('notification-declined-permanently') === 'true';
			if (hasDeclinedPermanently) {
				setHasCheckedSubscription(true);
				return;
			}

			// Check existing subscription
			const alreadySubscribed = await checkExistingSubscription();
			setIsSubscribed(alreadySubscribed);

			// Show prompt if not subscribed and hasn't declined permanently
			if (!alreadySubscribed) {
				// Check if user has seen the prompt today
				const lastPromptDate = localStorage.getItem('notification-prompt-date');
				const today = new Date().toDateString();

				if (lastPromptDate !== today) {
					setShowPrompt(true);
					localStorage.setItem('notification-prompt-date', today);
				}
			}

			setHasCheckedSubscription(true);
		};

		checkNotificationStatus();
	}, [userData]);

	const handleSubscribe = async () => {
		if (!userData) {
			toast.error('Please log in to enable notifications');
			return;
		}

		try {
			await subscribeNotificationMutation.mutateAsync();
			setIsSubscribed(true);
			setShowPrompt(false);
			toast.success(
				"🔔 Notifications enabled! You'll receive updates about new courses and messages.",
			);
		} catch (error) {
			console.error('Subscription error:', error);
			let errorMessage = 'Failed to enable notifications. Please try again.';

			if (error.message.includes('permission denied')) {
				errorMessage =
					'Notification permission denied. You can enable it in your browser settings.';
			} else if (error.message.includes('not supported')) {
				errorMessage = 'Push notifications are not supported in your browser.';
			}

			toast.error(errorMessage);
		}
	};

	const handleUnsubscribe = async () => {
		try {
			await unsubscribeNotificationMutation.mutateAsync();
			setIsSubscribed(false);
			toast.success('🔕You will no longer recieve notification.');
		} catch (error) {
			console.error('Unsubscription error:', error);
			toast.error('Failed to disable notifications. Please try again.');
		}
	};

	const handleDecline = () => {
		setShowPrompt(false);
		toast('You can enable notifications later in your settings.', {
			icon: '💡',
			duration: 3000,
		});
	};

	const handleDeclinePermanently = () => {
		localStorage.setItem('notification-declined-permanently', 'true');
		setShowPrompt(false);
		toast(
			'Notification prompts disabled. You can still enable notifications in settings.',
			{
				icon: 'ℹ️',
				duration: 4000,
			},
		);
	};

	// Don't render anything if we haven't checked subscription status yet
	if (!hasCheckedSubscription) {
		return null;
	}

	// Don't render if not authenticated or notifications not supported
	if (!isAuthenticated() || !isNotificationSupported()) {
		return null;
	}

	// Notification settings toggle (always visible when authenticated)
	const NotificationToggle = () => (
		<div className='fixed bottom-4 right-4 z-50 bg-white rounded-full shadow-lg border border-gray-200 cursor-pointer'>
			<button
				onClick={isSubscribed ? handleUnsubscribe : handleSubscribe}
				disabled={
					subscribeNotificationMutation.isLoading ||
					unsubscribeNotificationMutation.isLoading
				}
				className={`flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium cursor-pointer transition-all duration-200 ${
					isSubscribed
						? 'bg-green-100 text-green-800 hover:bg-green-200'
						: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
				} disabled:opacity-50 disabled:cursor-not-allowed`}
				title={isSubscribed ? 'Disable notifications' : 'Enable notifications'}>
				<FaBell className={isSubscribed ? 'text-green-600' : 'text-blue-600'} />
				{subscribeNotificationMutation.isLoading ||
				unsubscribeNotificationMutation.isLoading ? (
					<div className='animate-spin rounded-full h-4 w-4 border-b-2 border-current'></div>
				) : (
					<span className='hidden sm:inline'>
						{isSubscribed ? 'Notifications On' : 'Enable Notifications'}
					</span>
				)}
			</button>
		</div>
	);

	// Show prompt modal
	if (showPrompt) {
		return (
			<>
				<div className='fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4'>
					<div className='bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative'>
						<button
							onClick={handleDecline}
							className='absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors'>
							<FaTimes />
						</button>

						<div className='text-center'>
							<div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4'>
								<FaBell className='h-6 w-6 text-blue-600' />
							</div>

							<h3 className='text-lg font-semibold text-gray-900 mb-2'>
								Stay Updated with YouLife
							</h3>

							<p className='text-gray-600 mb-6'>
								Get notified instantly when new courses are available and when
								you receive messages. Never miss out on your learning journey!
							</p>

							<div className='space-y-3'>
								<button
									onClick={handleSubscribe}
									disabled={subscribeNotificationMutation.isLoading}
									className='w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors'>
									{subscribeNotificationMutation.isLoading ? (
										<div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
									) : (
										<>
											<FaCheck />
											Enable Notifications
										</>
									)}
								</button>

								<button
									onClick={handleDecline}
									className='w-full text-gray-600 hover:text-gray-800 font-medium py-2 px-4 transition-colors'>
									Maybe Later
								</button>

								<button
									onClick={handleDeclinePermanently}
									className='w-full text-gray-400 hover:text-gray-600 text-sm py-1 px-4 transition-colors'>
									Don't ask again
								</button>
							</div>
						</div>
					</div>
				</div>
				<NotificationToggle />
			</>
		);
	}

	// Show toggle button when not prompting
	return <NotificationToggle />;
};

export default NotificationPrompt;
