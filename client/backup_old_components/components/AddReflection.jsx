import { useState } from 'react';

const AddReflectionForm = ({ onSubmit, onCancel }) => {
	const [title, setTitle] = useState('');
	const [text, setText] = useState('');

	const handleSubmit = (e) => {
		e.preventDefault();

		// Format date as DD/MM/YYYY
		const today = new Date();
		const day = String(today.getDate()).padStart(2, '0');
		const month = String(today.getMonth() + 1).padStart(2, '0');
		const year = today.getFullYear();
		const formattedDate = `${day}/${month}/${year}`;

		onSubmit({
			title,
			text,
			date: formattedDate,
		});
	};

	return (
		<form onSubmit={handleSubmit}>
			<div className='mb-4'>
				<label className='block mb-2'>Title</label>
				<input
					type='text'
					value={title}
					onChange={(e) => setTitle(e.target.value)}
					className='w-full p-2 border rounded-lg bg-white shadow-md'
					required
				/>
			</div>

			<div className='mb-4'>
				<label className='block mb-2'>Reflection</label>
				<textarea
					value={text}
					onChange={(e) => setText(e.target.value)}
					className='w-full p-2 border rounded-lg bg-white shadow-md'
					rows='4'
					required></textarea>
			</div>

			<div className='flex justify-end font-semibold space-x-2'>
				<button
					type='button'
					onClick={onCancel}
					className='px-4 py-2 border rounded-lg border-gray-600'>
					Cancel
				</button>
				<button
					type='submit'
					className='px-4 py-2 bg-[#0C142A] text-white rounded-lg'>
					Save
				</button>
			</div>
		</form>
	);
};

export default AddReflectionForm;
