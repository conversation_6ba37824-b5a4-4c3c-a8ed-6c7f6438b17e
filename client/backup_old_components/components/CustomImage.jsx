'use client';
import { logo } from '@/assets/images';
import Image from 'next/image';
import React, { forwardRef, useState } from 'react';

const fallbackClassName = 'object-cover object-center h-full w-full bg-white';

const CustomImage = forwardRef(function CustomImage(
	{ src, fill = false, className = '', alt = '', width, height, ...props },
	ref,
) {
	const [hasError, setHasError] = useState(false);

	const imageSrc = hasError || !src ? logo : src;
	const imageClassName = hasError || !src ? fallbackClassName : className;

	return (
		<Image
			{...props}
			ref={ref}
			src={imageSrc}
			alt={alt || 'Image'}
			className={imageClassName}
			width={!fill ? width : undefined}
			height={!fill ? height : undefined}
			fill={fill}
			onError={() => setHasError(true)}
		/>
	);
});

CustomImage.displayName = 'CustomImage';

export default CustomImage;
