'use client';
import Link from 'next/link';
import React, { useState } from 'react';
import {
	FaCalendar,
	FaChevronLeft,
	FaChevronRight,
	FaHome,
} from 'react-icons/fa';
import { LuFileText, LuSettings } from 'react-icons/lu';
import { TbUsers } from 'react-icons/tb';
import { usePathname } from 'next/navigation';
import { useUserProfile } from '@/hooks/useUser';

const navLinks = [
	{
		label: 'Overview',
		href: '/admin/overview',
		icon: FaHome,
	},
	{
		label: 'User Management',
		href: '/admin/user-management',
		icon: TbUsers,
	},
	{
		label: 'Content Management',
		href: '/admin/content-management',
		icon: LuFileText,
	},
	{
		label: 'Booking Management',
		href: '/admin/booking-management',
		icon: FaCalendar,
	},
	{
		label: 'Settings',
		href: '/admin/settings',
		icon: LuSettings,
	},
];

const AdminSidebar = () => {
	const pathname = usePathname();
	const [isCollapsed, setIsCollapsed] = useState(false);
	const {
		data: userProfile,
		isLoading: isUserLoading,
		error: userError,
	} = useUserProfile();
	const toggleSidebar = () => {
		setIsCollapsed(!isCollapsed);
	};

	return (
		<div
			className={`relative rounded-2xl ml-2.5 bg-gradient-to-b from-teal-500 to-teal-600 text-white transition-all duration-300 ease-in-out ${
				isCollapsed ? 'w-16' : 'w-64'
			}`}>
			{/* Toggle Button */}
			<button
				onClick={toggleSidebar}
				className='absolute -right-3 top-6 bg-white text-teal-600 rounded-full p-1 shadow-lg hover:shadow-xl transition-all duration-200 z-10'>
				{isCollapsed ? (
					<FaChevronRight className='w-4 h-4' />
				) : (
					<FaChevronLeft className='w-4 h-4' />
				)}
			</button>

			{/* Navigation Links */}
			<nav className='mt-6 px-3'>
				<ul className='space-y-2'>
					{navLinks.map((link) => {
						const Icon = link.icon;
						const isActive = pathname === link.href;

						return (
							<Link
								href={link.href}
								key={link.href}>
								<button
									className={`w-full flex items-center px-3 py-3 rounded-lg transition-all duration-200 group ${
										isActive
											? 'bg-white/20 text-white shadow-lg'
											: 'text-teal-100 hover:bg-white/10 hover:text-white'
									}`}
									title={isCollapsed ? link.label : ''}>
									<Icon
										className={`w-5 h-5 ${isCollapsed ? 'mx-auto' : 'mr-3'} ${
											isActive ? 'scale-110' : 'group-hover:scale-105'
										} transition-transform duration-200`}
									/>
									{!isCollapsed && (
										<span className='font-medium text-left'>{link.label}</span>
									)}
									{!isCollapsed && isActive && (
										<div className='ml-auto w-2 h-2 bg-white rounded-full'></div>
									)}
								</button>
							</Link>
						);
					})}
				</ul>
			</nav>

			{/* Bottom Section */}
			<div className='absolute bottom-0 left-0 right-0 p-4 border-t border-teal-400/30'>
				<div
					className={`flex items-center ${
						isCollapsed ? 'justify-center' : 'space-x-3'
					}`}>
					<div className='w-8 h-8 bg-teal-400 rounded-full flex items-center justify-center'>
						<span className='text-white text-sm font-semibold'>
							{userProfile?.user?.name?.charAt(0)}
						</span>
					</div>
					{!isCollapsed && (
						<div className='flex-1 min-w-0'>
							<p className='text-white text-sm font-medium truncate'>
								{userProfile.user.name}
							</p>
							<p className='text-teal-200 text-xs truncate'>
								{userProfile.user.email}
							</p>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default AdminSidebar;
