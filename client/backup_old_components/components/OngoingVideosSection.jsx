import React from 'react';
import Link from 'next/link';
import { MdError } from 'react-icons/md';
import { useAllUserProgress } from '@/hooks/useUserProgress';
import OngoingVideoCard from './OngoingVideoCard';
import { VideoOff } from 'lucide-react';

const OngoingVideosSection = ({ isMobile = false }) => {
	const { data: userProgressData, isLoading, error } = useAllUserProgress();

	const ongoingVideos = userProgressData?.data || [];

	if (isLoading) {
		return (
			<section
				className={`${
					isMobile ? 'mx-4 mt-6' : 'mb-12'
				} rounded-3xl overflow-hidden bg-gradient-to-br from-teal-600 via-teal-700 to-teal-800 dark:from-white dark:via-gray-50 dark:to-gray-100 shadow-2xl`}>
				<div className={`${isMobile ? 'px-4 pt-6 pb-2' : 'px-6 pt-8 pb-3'}`}>
					<div className='flex justify-between items-center mb-4'>
						<div>
							<h2
								className={`${
									isMobile ? 'text-2xl' : 'text-3xl'
								} font-bold text-white dark:text-gray-900 mb-2`}>
								Continue Watching
							</h2>
							<p className='text-teal-100 dark:text-gray-600 text-sm'>
								Pick up where you left off
							</p>
						</div>
					</div>
				</div>
				<div className='px-4 pb-4'>
					<div className='bg-white/10 dark:bg-gray-200/50 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-300/30'>
						<div className='flex flex-col items-center justify-center py-12'>
							<div className='animate-spin rounded-full h-12 w-12 border-4 border-teal-600 border-t-transparent mb-4'></div>
							<p className='text-white/80 dark:text-gray-600 text-sm'>
								Loading your progress...
							</p>
						</div>
					</div>
				</div>
			</section>
		);
	}

	if (error) {
		return (
			<section
				className={`${
					isMobile ? 'mx-4 mt-6' : 'mb-12'
				} rounded-3xl overflow-hidden bg-gradient-to-br from-teal-600 via-teal-700 to-teal-800 dark:from-white dark:via-gray-50 dark:to-gray-100 shadow-2xl`}>
				<div className={`${isMobile ? 'px-4 pt-6 pb-2' : 'px-6 pt-8 pb-3'}`}>
					<div className='flex justify-between items-center mb-4'>
						<div>
							<h2
								className={`${
									isMobile ? 'text-2xl' : 'text-3xl'
								} font-bold text-white dark:text-gray-900 mb-2`}>
								Continue Watching
							</h2>
							<p className='text-teal-100 dark:text-gray-600 text-sm'>
								Pick up where you left off
							</p>
						</div>
					</div>
				</div>
				<div className='px-4 pb-4'>
					<div className='bg-white/10 dark:bg-gray-200/50 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-300/30'>
						<div className='flex flex-col items-center justify-center py-12 text-center'>
							<div className='bg-red-50/20 dark:bg-red-900/20 backdrop-blur-sm rounded-full p-4 mb-4 border border-red-300/30'>
								<MdError className='w-8 h-8 text-red-300 dark:text-red-400' />
							</div>
							<h3 className='text-lg font-semibold text-white dark:text-gray-900 mb-2'>
								Unable to load progress
							</h3>
							<p className='text-white/80 dark:text-gray-600 text-sm'>
								Please try refreshing the page
							</p>
						</div>
					</div>
				</div>
			</section>
		);
	}

	if (!ongoingVideos || ongoingVideos.length === 0) {
		return (
			<section
				className={`${
					isMobile ? 'mx-4 mt-6' : 'mb-12'
				} rounded-3xl overflow-hidden bg-gradient-to-br from-teal-600 via-teal-700 to-teal-800 dark:from-white dark:via-gray-50 dark:to-gray-100 shadow-2xl`}>
				<div className={`${isMobile ? 'px-4 pt-6 pb-2' : 'px-6 pt-8 pb-3'}`}>
					<div className='flex justify-between items-center mb-4'>
						<div>
							<h2
								className={`${
									isMobile ? 'text-2xl' : 'text-3xl'
								} font-bold text-white dark:text-gray-900 mb-2`}>
								Continue Watching
							</h2>
							<p className='text-teal-100 dark:text-gray-600 text-sm'>
								Pick up where you left off
							</p>
						</div>
						<Link
							href='/lessons'
							className='text-white dark:text-teal-600 text-sm font-semibold hover:text-teal-100 dark:hover:text-teal-700 transition-colors duration-200 bg-white/10 dark:bg-teal-50 px-4 py-2 rounded-full border border-white/20 dark:border-teal-200 backdrop-blur-sm'>
							Browse Lessons
						</Link>
					</div>
				</div>
				<div className='px-4 pb-4'>
					<div className='bg-white/10 dark:bg-gray-200/50 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-300/30'>
						<div className='flex flex-col items-center justify-center py-12 text-center'>
							<div className='bg-white/10 dark:bg-gray-200/50 backdrop-blur-sm rounded-full p-6 mb-6 border border-white/20 dark:border-gray-300/30'>
								<VideoOff className='w-8 h-8 text-white/80 dark:text-gray-600' />
							</div>
							<h3 className='text-xl font-semibold text-white dark:text-gray-900 mb-3'>
								No videos in progress
							</h3>
							<p className='text-teal-100 dark:text-gray-600 max-w-md'>
								Start watching a lesson to see your progress here. Your learning
								journey begins with a single click!
							</p>
						</div>
					</div>
				</div>
			</section>
		);
	}

	return (
		<section
			className={`${
				isMobile ? 'mx-4 mt-6' : 'mb-12'
			} rounded-3xl overflow-hidden bg-gradient-to-br from-teal-600 via-teal-700 to-teal-800 dark:from-white dark:via-gray-50 dark:to-gray-100 shadow-2xl`}>
			<div className={`${isMobile ? 'px-4 pt-6 pb-2' : 'px-6 pt-8 pb-3'}`}>
				<div className='flex justify-between items-center mb-4'>
					<div>
						<h2
							className={`${
								isMobile ? 'text-2xl' : 'text-3xl'
							} font-bold text-white dark:text-gray-900 mb-2`}>
							Continue Watching
						</h2>
						<p className='text-teal-100 dark:text-gray-600 text-sm'>
							Pick up where you left off
						</p>
					</div>
					<Link
						href='/lessons'
						className='text-white dark:text-teal-600 text-sm font-semibold hover:text-teal-100 dark:hover:text-teal-700 transition-colors duration-200 bg-white/10 dark:bg-teal-50 px-4 py-2 rounded-full border border-white/20 dark:border-teal-200 backdrop-blur-sm'>
						View All
					</Link>
				</div>
			</div>

			<div className='px-4 pb-4'>
				{/* Single video card display with reduced padding */}
				<div className='flex justify-center'>
					<OngoingVideoCard userProgress={ongoingVideos[0]} />
				</div>
			</div>
		</section>
	);
};

export default OngoingVideosSection;
