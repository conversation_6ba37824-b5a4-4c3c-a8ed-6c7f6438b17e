'use client';
import React from 'react';
import { FaChevronLeft } from 'react-icons/fa';
import { FcMenu } from 'react-icons/fc';
import { useRouter } from 'next/navigation';

const FixedHeader = ({
	toggleSidebar,
	title = 'Lessons',
	showBackButton = true,
}) => {
	const router = useRouter();

	const handleBack = () => {
		router.back();
	};

	return (
		<div className='flex items-center justify-between p-4 sticky top-0 z-10 bg-white dark:bg-[#0C142A] '>
			<div className='flex items-center space-x-2'>
				<button
					className='p-2 rounded-full'
					onClick={toggleSidebar}>
					<FcMenu
						size={32}
						className='dark:text-white text-black'
					/>
				</button>
			</div>
			<h1 className='text-2xl font-bold dark:text-white text-black'>{title}</h1>

			<button
				className='p-2 rounded-full'
				onClick={handleBack}>
				<FaChevronLeft
					size={24}
					className='dark:text-white text-black'
				/>
			</button>
		</div>
	);
};

export default FixedHeader;
