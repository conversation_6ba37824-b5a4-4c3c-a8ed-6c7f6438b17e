'use client';
import { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
	FaMinus,
	FaPause,
	FaPlay,
	FaPlus,
	FaEdit,
	FaTrash,
	FaExpand,
	FaCompress,
	FaTimes,
	FaSave,
} from 'react-icons/fa';
import CustomImage from '@/components/CustomImage';
import Sidebar from '@/components/Sidebar';
import FixedHeader from './FixedHeader';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';
import throttle from 'lodash.throttle';
import { useDeleteLesson } from '@/hooks/useLessons';
import {
	useUserProgress,
	useUpdateUserProgress,
	useUpdateReflection,
	useDeleteReflection,
} from '@/hooks/useUserProgress';

const ReactPlayer = dynamic(() => import('react-player/lazy'), {
	ssr: false,
	loading: () => (
		<div className='w-full h-full flex items-center justify-center bg-black'>
			<div className='animate-pulse text-white'>Loading player...</div>
		</div>
	),
});

export default function VideoScreen({ lesson, allLessons, canManageLessons }) {
	const router = useRouter();
	const deleteLessonMutation = useDeleteLesson();
	const { updateProgress, forceUpdateProgress, flushPendingUpdates } =
		useUpdateUserProgress();
	const updateReflectionMutation = useUpdateReflection();
	const deleteReflectionMutation = useDeleteReflection();

	// Get user progress for this lesson
	const { data: userProgress, isLoading: progressLoading } = useUserProgress(
		lesson._id,
	);

	const [isSidebarOpen, setIsSidebarOpen] = useState(false);
	const [isPlaying, setIsPlaying] = useState(false);
	const [isReflectionOpen, setIsReflectionOpen] = useState(false);
	const [playerReady, setPlayerReady] = useState(false);
	const [progress, setProgress] = useState(0);
	const [volume, setVolume] = useState(0.8);
	const [played, setPlayed] = useState(0);
	const [seeking, setSeeking] = useState(false);
	const [bufferState, setBufferState] = useState(0);
	const [showControls, setShowControls] = useState(true);
	const [controlsTimeout, setControlsTimeout] = useState(null);
	const [showFull, setShowFull] = useState(false);
	const [isFullscreen, setIsFullscreen] = useState(false);
	const [isMobile, setIsMobile] = useState(false);

	// Reflection state
	const [reflectionAnswers, setReflectionAnswers] = useState({});
	const [notes, setNotes] = useState('');

	// Refs for tracking significant progress changes
	const lastSavedProgressRef = useRef(0);
	const lastPlayStateRef = useRef(false);

	// Initialize reflection data from user progress
	useEffect(() => {
		if (userProgress?.data) {
			const progress = userProgress.data;

			// Initialize reflection answers
			const answersMap = {};
			if (progress.reflections) {
				progress.reflections.forEach((reflection) => {
					answersMap[reflection.questionIndex] = reflection.answer;
				});
			}
			setReflectionAnswers(answersMap);

			// Initialize notes
			setNotes(progress.notes || '');
		}
	}, [userProgress]);

	const maxChars = 90;
	const playerRef = useRef(null);
	const videoContainerRef = useRef(null);
	const progressRestoredRef = useRef(false);

	// Check if device is mobile
	useEffect(() => {
		const checkMobile = () => {
			setIsMobile(
				window.innerWidth <= 768 ||
					/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
						navigator.userAgent,
					),
			);
		};

		checkMobile();
		window.addEventListener('resize', checkMobile);
		return () => window.removeEventListener('resize', checkMobile);
	}, []);

	// Handle fullscreen changes
	useEffect(() => {
		const handleFullscreenChange = () => {
			const isCurrentlyFullscreen = !!(
				document.fullscreenElement ||
				document.webkitFullscreenElement ||
				document.mozFullScreenElement ||
				document.msFullscreenElement
			);
			setIsFullscreen(isCurrentlyFullscreen);

			// Handle mobile orientation
			if (isMobile) {
				if (isCurrentlyFullscreen) {
					// Lock to landscape when entering fullscreen on mobile
					if (screen.orientation && screen.orientation.lock) {
						screen.orientation.lock('landscape').catch(console.error);
					}
				} else {
					// Unlock orientation when exiting fullscreen
					if (screen.orientation && screen.orientation.unlock) {
						screen.orientation.unlock();
					}
				}
			}
		};

		document.addEventListener('fullscreenchange', handleFullscreenChange);
		document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
		document.addEventListener('mozfullscreenchange', handleFullscreenChange);
		document.addEventListener('MSFullscreenChange', handleFullscreenChange);

		return () => {
			document.removeEventListener('fullscreenchange', handleFullscreenChange);
			document.removeEventListener(
				'webkitfullscreenchange',
				handleFullscreenChange,
			);
			document.removeEventListener(
				'mozfullscreenchange',
				handleFullscreenChange,
			);
			document.removeEventListener(
				'MSFullscreenChange',
				handleFullscreenChange,
			);
		};
	}, [isMobile]);

	// Toggle fullscreen function
	const toggleFullscreen = async () => {
		if (!videoContainerRef.current) return;

		try {
			if (!isFullscreen) {
				// Enter fullscreen
				if (videoContainerRef.current.requestFullscreen) {
					await videoContainerRef.current.requestFullscreen();
				} else if (videoContainerRef.current.webkitRequestFullscreen) {
					await videoContainerRef.current.webkitRequestFullscreen();
				} else if (videoContainerRef.current.mozRequestFullScreen) {
					await videoContainerRef.current.mozRequestFullScreen();
				} else if (videoContainerRef.current.msRequestFullscreen) {
					await videoContainerRef.current.msRequestFullscreen();
				}
			} else {
				// Exit fullscreen
				if (document.exitFullscreen) {
					await document.exitFullscreen();
				} else if (document.webkitExitFullscreen) {
					await document.webkitExitFullscreen();
				} else if (document.mozCancelFullScreen) {
					await document.mozCancelFullScreen();
				} else if (document.msExitFullscreen) {
					await document.msExitFullscreen();
				}
			}
		} catch (error) {
			console.error('Fullscreen error:', error);
		}
	};

	const toggleShow = () => setShowFull((prev) => !prev);

	const shouldTruncate = lesson?.description?.length > maxChars;
	const displayedText =
		showFull || !shouldTruncate
			? lesson?.description
			: `${lesson?.description?.substring(0, maxChars)}...`;

	const nextLesson = allLessons?.find((l) => l.id === lesson.id + 1) || null;

	const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

	const togglePlay = () => {
		const newPlayState = !isPlaying;
		setIsPlaying(newPlayState);

		// Force update progress when play state changes
		if (played > 0) {
			forceUpdateProgress({
				lessonId: lesson._id,
				videoProgress: played,
				watchTime: Math.floor(played * (playerRef.current?.getDuration() || 0)),
				isPlaying: newPlayState,
			});
		}

		lastPlayStateRef.current = newPlayState;
		showControlsTemporarily();
	};

	const toggleReflection = () => setIsReflectionOpen(!isReflectionOpen);

	const navigateToNextLesson = () => {
		if (nextLesson) {
			// Force save progress before navigation
			if (played > 0) {
				forceUpdateProgress({
					lessonId: lesson._id,
					videoProgress: played,
					watchTime: Math.floor(
						played * (playerRef.current?.getDuration() || 0),
					),
					isPlaying: false,
				});
			}
			router.push(`/lessons/${nextLesson.id}`);
		}
	};

	// Show controls temporarily and hide after 3 seconds
	const showControlsTemporarily = () => {
		setShowControls(true);
		if (controlsTimeout) {
			clearTimeout(controlsTimeout);
		}
		const timeout = setTimeout(() => {
			if (isPlaying) {
				setShowControls(false);
			}
		}, 3000);
		setControlsTimeout(timeout);
	};

	// Optimized progress handler - only updates locally, batches server updates
	const throttledHandleProgress = throttle((state) => {
		if (!seeking && lesson._id) {
			const newPlayed = state.played;
			const newProgress = newPlayed * 100;

			setPlayed(newPlayed);
			setProgress(newProgress);
			setBufferState(state.loaded * 100);

			// Check if we should update server (every 10% or significant change)
			const progressDifference = Math.abs(
				newPlayed - lastSavedProgressRef.current,
			);
			const shouldUpdate =
				progressDifference >= 0.1 || // Every 10%
				(progressDifference >= 0.05 && Date.now() % 30000 < 1000); // Every 5% but max once per 30 seconds

			if (shouldUpdate) {
				updateProgress({
					lessonId: lesson._id,
					videoProgress: newPlayed,
					watchTime: Math.floor(
						newPlayed * (playerRef.current?.getDuration() || 0),
					),
				});
				lastSavedProgressRef.current = newPlayed;
			}
		}
	}, 1000);

	const handleProgress = (state) => throttledHandleProgress(state);
	const handleVolumeChange = (e) => setVolume(parseFloat(e.target.value));
	const handleSeekMouseDown = () => setSeeking(true);
	const handleSeekChange = (e) => {
		const seekTo = parseFloat(e.target.value) / 100;
		setPlayed(seekTo);
		setProgress(parseFloat(e.target.value));
	};
	const handleSeekMouseUp = (e) => {
		setSeeking(false);
		const seekTo = parseFloat(e.target.value) / 100;
		playerRef.current?.seekTo(seekTo, 'fraction');
		setIsPlaying(true);

		// Force update progress when user seeks
		forceUpdateProgress({
			lessonId: lesson._id,
			videoProgress: seekTo,
			watchTime: Math.floor(seekTo * (playerRef.current?.getDuration() || 0)),
		});
		lastSavedProgressRef.current = seekTo;
	};

	const handleReady = () => {
		setPlayerReady(true);
	};

	const handlePlay = () => {
		setIsPlaying(true);
		lastPlayStateRef.current = true;
	};

	const handlePause = () => {
		setIsPlaying(false);
		setShowControls(true);

		// Force update progress when paused
		if (played > 0) {
			forceUpdateProgress({
				lessonId: lesson._id,
				videoProgress: played,
				watchTime: Math.floor(played * (playerRef.current?.getDuration() || 0)),
				isPlaying: false,
			});
		}
		lastPlayStateRef.current = false;
	};

	const handleEnded = () => {
		console.log('ReactPlayer ended');
		setIsPlaying(false);
		setShowControls(true);

		// Force update progress when video ends
		forceUpdateProgress({
			lessonId: lesson._id,
			videoProgress: 1, // Mark as completed
			watchTime: Math.floor(playerRef.current?.getDuration() || 0),
			isPlaying: false,
			completed: true,
		});
		lastPlayStateRef.current = false;
	};

	const handleError = (error) => {
		console.error('ReactPlayer error:', error);
		console.error('Video URL:', lesson.videoUrl);
		setIsPlaying(false);
	};

	const handlePlayerClick = () => {
		togglePlay();
	};

	// Mouse movement handler to show controls
	const handleMouseMove = () => {
		showControlsTemporarily();
	};

	// Reflection handlers
	const handleReflectionAnswerChange = (questionIndex, answer) => {
		setReflectionAnswers((prev) => ({
			...prev,
			[questionIndex]: answer,
		}));
	};

	const handleNotesChange = (newNotes) => {
		setNotes(newNotes);
	};

	const saveReflectionAnswer = useCallback(
		async (questionIndex, answer) => {
			try {
				await updateReflectionMutation.mutateAsync({
					lessonId: lesson._id,
					questionIndex,
					answer,
				});
			} catch (error) {
				console.error('Error saving reflection:', error);
			}
		},
		[lesson._id, updateReflectionMutation],
	);

	const saveNotes = useCallback(
		async (notesContent) => {
			try {
				forceUpdateProgress({
					lessonId: lesson._id,
					notes: notesContent,
				});
			} catch (error) {
				console.error('Error saving notes:', error);
			}
		},
		[lesson._id, forceUpdateProgress],
	);

	const deleteReflectionAnswer = useCallback(
		async (questionIndex) => {
			try {
				await deleteReflectionMutation.mutateAsync({
					lessonId: lesson._id,
					questionIndex,
				});
				setReflectionAnswers((prev) => {
					const newAnswers = { ...prev };
					delete newAnswers[questionIndex];
					return newAnswers;
				});
			} catch (error) {
				console.error('Error deleting reflection:', error);
			}
		},
		[lesson._id, deleteReflectionMutation],
	);

	// Restore progress when player is ready
	useEffect(() => {
		if (lesson._id && playerReady && !progressRestoredRef.current) {
			// Prioritize backend progress over localStorage
			let progressToRestore = 0;

			// Check backend progress first
			if (userProgress?.data?.videoProgress) {
				progressToRestore = userProgress.data.videoProgress;
			} else {
				// Fallback to localStorage if no backend progress
				const savedProgress = localStorage.getItem(
					`lesson_progress_${lesson._id}`,
				);
				if (savedProgress) {
					progressToRestore = parseFloat(savedProgress);
				}
			}

			// Restore progress if we have any
			if (progressToRestore > 0) {
				setPlayed(progressToRestore);
				setProgress(progressToRestore * 100);
				playerRef.current?.seekTo(progressToRestore, 'fraction');
				lastSavedProgressRef.current = progressToRestore;
			}

			// Mark progress as restored for this lesson
			progressRestoredRef.current = true;
		}
	}, [lesson._id, playerReady, userProgress]);

	// Reset progress restored flag when lesson changes
	useEffect(() => {
		progressRestoredRef.current = false;
		lastSavedProgressRef.current = 0;
	}, [lesson._id]);

	// Handle page visibility changes to flush pending updates
	useEffect(() => {
		const handleVisibilityChange = () => {
			if (document.visibilityState === 'hidden') {
				// Page is being hidden, flush pending updates
				flushPendingUpdates();
			}
		};

		const handleBeforeUnload = () => {
			// Page is being unloaded, flush pending updates
			flushPendingUpdates();
		};

		document.addEventListener('visibilitychange', handleVisibilityChange);
		window.addEventListener('beforeunload', handleBeforeUnload);

		return () => {
			document.removeEventListener('visibilitychange', handleVisibilityChange);
			window.removeEventListener('beforeunload', handleBeforeUnload);
		};
	}, [flushPendingUpdates]);

	// Cleanup timeout on unmount and flush final updates
	useEffect(() => {
		return () => {
			if (controlsTimeout) {
				clearTimeout(controlsTimeout);
			}

			// Flush any pending updates when component unmounts
			flushPendingUpdates();
		};
	}, [controlsTimeout, flushPendingUpdates]);

	return (
		<div className='flex min-h-screen bg-gray-100 dark:bg-gray-900'>
			{/* Mobile Sidebar */}
			<div
				className={`fixed inset-0 z-40 transform ${
					isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
				} transition-transform duration-300 ease-in-out`}>
				<Sidebar
					isOpen={isSidebarOpen}
					onToggle={toggleSidebar}
				/>
			</div>

			{/* Main Content */}
			<div className='flex-1 flex flex-col'>
				<FixedHeader
					toggleSidebar={toggleSidebar}
					title={lesson.title}
					showBackButton={true}
					className='z-30'
				/>

				<div className='flex-1 pt-1'>
					{/* Video Player */}
					<div
						ref={videoContainerRef}
						className={`relative w-full bg-black max-w-7xl mx-auto ${
							isFullscreen
								? 'fixed inset-0 z-50 max-w-none h-screen'
								: 'aspect-video'
						}`}
						onMouseMove={handleMouseMove}
						onMouseLeave={() => {
							if (isPlaying) {
								setShowControls(false);
							}
						}}>
						<ReactPlayer
							ref={playerRef}
							url={lesson.videoUrl}
							width='100%'
							height='100%'
							playing={isPlaying}
							volume={volume}
							muted={volume === 0}
							playsinline={true}
							controls={false}
							onReady={handleReady}
							onPlay={handlePlay}
							onPause={handlePause}
							onEnded={handleEnded}
							onError={handleError}
							onProgress={handleProgress}
							progressInterval={1000}
							config={{
								file: {
									attributes: {
										controlsList: 'nodownload',
										onContextMenu: (e) => e.preventDefault(),
										preload: 'auto',
									},
									forceVideo: true,
								},
								youtube: {
									playerVars: { modestbranding: 1, rel: 0, showinfo: 0 },
								},
							}}
							className='object-contain'
						/>

						{/* Click overlay for play/pause */}
						<div
							className='absolute inset-0 cursor-pointer'
							onClick={handlePlayerClick}
						/>

						{/* Play button overlay (only when paused) */}
						{!isPlaying && playerReady && (
							<div className='absolute inset-0 flex items-center justify-center pointer-events-none'>
								<div className='rounded-full bg-black bg-opacity-50 p-6'>
									<FaPlay
										size={48}
										className='text-white'
									/>
								</div>
							</div>
						)}

						{/* Custom Controls */}
						<div
							className={`absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 px-4 py-3 transition-opacity duration-300 ${
								showControls || !isPlaying ? 'opacity-100' : 'opacity-0'
							}`}>
							<div className='relative w-full h-2 bg-gray-600 rounded'>
								<div
									className='absolute top-0 left-0 h-full bg-gray-400 rounded'
									style={{ width: `${bufferState}%` }}
								/>
								<input
									type='range'
									min={0}
									max={100}
									step='any'
									value={progress}
									onMouseDown={handleSeekMouseDown}
									onChange={handleSeekChange}
									onMouseUp={handleSeekMouseUp}
									onTouchEnd={handleSeekMouseUp}
									className='absolute top-0 left-0 w-full h-2 appearance-none bg-transparent cursor-pointer z-10'
									style={{
										'--range-color': '#00aaff',
										WebkitAppearance: 'none',
									}}
								/>
								<div
									className='absolute top-0 left-0 h-full bg-blue-500 rounded pointer-events-none'
									style={{ width: `${progress}%` }}
								/>
							</div>
							<div className='flex justify-between items-center mt-3'>
								<div className='flex items-center gap-2'>
									<button
										onClick={(e) => {
											e.stopPropagation();
											togglePlay();
										}}
										className='text-white px-2 py-1 rounded hover:bg-gray-700 transition'>
										{isPlaying ? <FaPause size={16} /> : <FaPlay size={16} />}
									</button>
									<button
										onClick={(e) => {
											e.stopPropagation();
											toggleFullscreen();
										}}
										className='text-white px-2 py-1 rounded hover:bg-gray-700 transition'>
										{isFullscreen ? (
											<FaCompress size={16} />
										) : (
											<FaExpand size={16} />
										)}
									</button>
								</div>
								<div className='flex items-center'>
									<span className='text-white text-sm mr-2'>Volume</span>
									<input
										type='range'
										min={0}
										max={1}
										step={0.05}
										value={volume}
										onChange={handleVolumeChange}
										className='w-20 h-1 cursor-pointer'
									/>
								</div>
							</div>
						</div>
					</div>

					{/* Lesson Content - Hidden in fullscreen */}
					{!isFullscreen && (
						<div className='max-w-7xl mx-auto px-6 py-8'>
							<div className='flex flex-col md:flex-row md:items-center md:justify-between mb-6'>
								<h2 className='text-2xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4 md:mb-0'>
									{lesson.title}
								</h2>

								{/* Management Controls for Authorized Users */}
								{canManageLessons && (
									<div className='flex gap-2'>
										<button
											onClick={() =>
												router.push(`/admin/lessons/edit/${lesson._id}`)
											}
											className='flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors'>
											<FaEdit />
											Edit
										</button>
										<button
											onClick={async () => {
												if (
													confirm(
														'Are you sure you want to delete this lesson? This action cannot be undone.',
													)
												) {
													try {
														await deleteLessonMutation.mutateAsync(lesson._id);
														alert('Lesson deleted successfully!');
														router.push('/lessons');
													} catch (error) {
														console.error('Delete error:', error);
														alert(
															error?.response?.data?.message ||
																'Failed to delete lesson',
														);
													}
												}
											}}
											disabled={deleteLessonMutation.isLoading}
											className='flex items-center gap-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-2 rounded-lg transition-colors'>
											<FaTrash />
											{deleteLessonMutation.isLoading
												? 'Deleting...'
												: 'Delete'}
										</button>
									</div>
								)}
							</div>
							<div className='flex items-center mb-6'>
								<div className='w-14 h-14 bg-gray-300 dark:bg-gray-700 rounded-full flex items-center justify-center overflow-hidden'>
									<CustomImage
										src='/student-icon.svg'
										alt='Instructor icon'
										width={36}
										height={36}
									/>
								</div>
								<span className='ml-4 text-lg md:text-2xl font-semibold text-gray-800 dark:text-white'>
									{lesson.instructor}
								</span>
							</div>
							<div className='text-xl text-gray-800 dark:text-white mb-8'>
								<p>{displayedText}</p>
								{shouldTruncate && (
									<button
										onClick={toggleShow}
										className='text-blue-600 dark:text-blue-400 hover:underline mt-2'>
										{showFull ? 'Show less' : 'Show more'}
									</button>
								)}
							</div>

							{/* Lesson Reflection */}
							{(lesson.reflectionQuestions?.length > 0 || true) && (
								<div className='border-t border-b border-gray-300 dark:border-gray-700'>
									<button
										onClick={toggleReflection}
										className='flex items-center justify-between w-full py-4 text-left text-2xl font-bold text-gray-800 dark:text-white'>
										<span>Lesson Reflection</span>
										{isReflectionOpen ? (
											<FaMinus
												size={24}
												className='cursor-pointer'
											/>
										) : (
											<FaPlus
												size={24}
												className='cursor-pointer'
											/>
										)}
									</button>
									<AnimatePresence>
										{isReflectionOpen && (
											<motion.div
												initial={{ opacity: 0, y: -20 }}
												animate={{ opacity: 1, y: 0 }}
												exit={{ opacity: 0, y: -20 }}
												transition={{ duration: 0.3 }}
												className='p-6 bg-gray-200 dark:bg-gray-50 dark:text-black rounded-2xl space-y-6'>
												{/* Dynamic Reflection Questions */}
												{lesson.reflectionQuestions?.map((question, index) => (
													<div
														key={index}
														className='space-y-3'>
														<div className='flex items-start justify-between'>
															<h4 className='font-semibold text-lg text-gray-800'>
																{index + 1}. {question}
															</h4>
															{reflectionAnswers[index] && (
																<button
																	onClick={() => deleteReflectionAnswer(index)}
																	className='text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50 transition-colors'
																	title='Delete answer'>
																	<FaTimes size={14} />
																</button>
															)}
														</div>
														<div className='space-y-2'>
															<textarea
																value={reflectionAnswers[index] || ''}
																onChange={(e) =>
																	handleReflectionAnswerChange(
																		index,
																		e.target.value,
																	)
																}
																className='w-full p-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:outline-none resize-none'
																rows={4}
																placeholder='Write your reflection here...'
															/>
															<div className='flex justify-end'>
																<button
																	onClick={() =>
																		saveReflectionAnswer(
																			index,
																			reflectionAnswers[index] || '',
																		)
																	}
																	disabled={!reflectionAnswers[index]?.trim()}
																	className='px-4 py-2 bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 text-white rounded-lg transition-colors text-sm flex items-center gap-2'>
																	<FaSave size={12} />
																	Save Answer
																</button>
															</div>
														</div>
													</div>
												))}

												{/* Notes Section */}
												<div className='space-y-3 border-t pt-6'>
													<div className='flex items-center justify-between'>
														<h4 className='font-semibold text-lg text-gray-800'>
															Personal Notes
														</h4>
														{notes && (
															<button
																onClick={() => {
																	setNotes('');
																	saveNotes('');
																}}
																className='text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50 transition-colors'
																title='Clear notes'>
																<FaTimes size={14} />
															</button>
														)}
													</div>
													<div className='space-y-2'>
														<textarea
															value={notes}
															onChange={(e) =>
																handleNotesChange(e.target.value)
															}
															className='w-full p-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:outline-none resize-none'
															rows={4}
															placeholder='Add your personal notes about this lesson...'
														/>
														<div className='flex justify-end'>
															<button
																onClick={() => saveNotes(notes)}
																className='px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg transition-colors text-sm flex items-center gap-2'>
																<FaSave size={12} />
																Save Notes
															</button>
														</div>
													</div>
												</div>

												{/* Show message if no reflection questions */}
												{(!lesson.reflectionQuestions ||
													lesson.reflectionQuestions.length === 0) && (
													<div className='text-center py-8 text-gray-600'>
														<p>
															No reflection questions available for this lesson.
														</p>
														<p className='text-sm mt-2'>
															You can still add personal notes above.
														</p>
													</div>
												)}
											</motion.div>
										)}
									</AnimatePresence>
								</div>
							)}

							{/* Play Next Section */}
							{nextLesson && (
								<div className='mt-8'>
									<h3 className='mb-4 text-2xl font-bold text-gray-800 dark:text-white'>
										Play Next
									</h3>
									<div
										className='relative rounded-lg overflow-hidden cursor-pointer shadow-md hover:shadow-lg transition max-w-md'
										onClick={navigateToNextLesson}>
										<div className='relative h-48 w-full'>
											<CustomImage
												src={nextLesson.image || '/placeholder.jpg'}
												alt={nextLesson.title}
												fill
												className='object-cover'
											/>
										</div>
										<div className='p-4 bg-gray-200 dark:bg-white text-black'>
											<h4 className='text-xl font-bold'>{nextLesson.title}</h4>
										</div>
									</div>
								</div>
							)}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
