'use client';

import VideoScreen from './VideoScreen';
import { useLesson, useLessons, useCanManageLessons } from '@/hooks/useLessons';
import { useRouter } from 'next/navigation';

export default function VideoLessonClient({ lessonId }) {
	const router = useRouter();
	const canManageLessons = useCanManageLessons();

	// Fetch current lesson
	const {
		data: lessonData,
		isLoading: lessonLoading,
		error: lessonError,
	} = useLesson(lessonId);

	// Fetch all lessons for sidebar
	const { data: allLessonsData, isLoading: allLessonsLoading } = useLessons({
		limit: 100,
	});

	if (lessonLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen'>
				<div className='animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900'></div>
			</div>
		);
	}

	if (lessonError || !lessonData?.data?.data) {
		return (
			<div className='flex items-center justify-center min-h-screen'>
				<div className='text-center'>
					<h2 className='text-2xl font-bold mb-2'>Lesson not found</h2>
					<p className='text-gray-600 mb-4'>
						The lesson you're looking for doesn't exist.
					</p>
					<button
						onClick={() => router.push('/lessons')}
						className='bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded'>
						Back to Lessons
					</button>
				</div>
			</div>
		);
	}

	const lesson = lessonData.data.data;
	const allLessons = allLessonsData?.data?.lessons || [];

	// Don't render VideoScreen until we have a valid lesson with _id
	if (!lesson || !lesson._id) {
		return (
			<div className='flex items-center justify-center min-h-screen'>
				<div className='animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900'></div>
			</div>
		);
	}

	return (
		<VideoScreen
			lesson={lesson}
			allLessons={allLessons}
			canManageLessons={canManageLessons}
		/>
	);
}
