'use client';
import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useUploadLesson, useLessonCategories } from '@/hooks/useLessons';
import {
	FaUpload,
	FaArrowLeft,
	FaCheckCircle,
	FaExclamationTriangle,
	FaPlus,
	FaMinus,
} from 'react-icons/fa';
import AdminLayout from './AdminLayout';
import CloudinaryUpload from './CloudinaryUpload';

const ErrorAlert = ({ error, onDismiss }) => (
	<div className='bg-red-50 border border-red-200 rounded-lg p-4 mb-6'>
		<div className='flex items-center gap-2 text-red-800'>
			<FaExclamationTriangle />
			<span className='font-medium'>Upload Error</span>
		</div>
		<p className='text-red-700 mt-1'>{error}</p>
		<button
			onClick={onDismiss}
			className='text-red-600 underline text-sm mt-2 hover:text-red-800'>
			Dismiss
		</button>
	</div>
);

export default function LessonUploadForm() {
	const router = useRouter();
	const { data: categories } = useLessonCategories();
	const uploadLessonMutation = useUploadLesson();

	const [formData, setFormData] = useState({
		title: '',
		instructor: '',
		category: '',
		description: '',
	});

	const [files, setFiles] = useState({
		thumbnail: null,
		video: null,
	});

	const [reflectionQuestions, setReflectionQuestions] = useState(['']);

	const [uploadState, setUploadState] = useState({
		isUploading: false,
		error: null,
		success: false,
	});

	const handleInputChange = useCallback((e) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));
	}, []);

	const handleVideoUploadSuccess = useCallback((uploadResult) => {
		if (uploadResult) {
			setFiles((prev) => ({ ...prev, video: uploadResult.url }));
		} else {
			setFiles((prev) => ({ ...prev, video: null }));
		}
	}, []);

	const handleThumbnailUploadSuccess = useCallback((uploadResult) => {
		if (uploadResult) {
			setFiles((prev) => ({ ...prev, thumbnail: uploadResult.url }));
		} else {
			setFiles((prev) => ({ ...prev, thumbnail: null }));
		}
	}, []);

	const handleUploadError = useCallback((error) => {
		console.error('Upload error:', error);
		setUploadState((prev) => ({
			...prev,
			error: error?.message || 'Upload failed',
		}));
	}, []);

	const dismissError = useCallback(() => {
		setUploadState((prev) => ({ ...prev, error: null }));
	}, []);

	const handleBack = useCallback(() => {
		if (!uploadState.isUploading) {
			router.back();
		}
	}, [router, uploadState.isUploading]);

	// Reflection question handlers
	const handleReflectionQuestionChange = useCallback((index, value) => {
		setReflectionQuestions(prev => {
			const newQuestions = [...prev];
			newQuestions[index] = value;
			return newQuestions;
		});
	}, []);

	const addReflectionQuestion = useCallback(() => {
		setReflectionQuestions(prev => [...prev, '']);
	}, []);

	const removeReflectionQuestion = useCallback((index) => {
		setReflectionQuestions(prev => prev.filter((_, i) => i !== index));
	}, []);

	const handleSubmit = useCallback(
		async (e) => {
			e.preventDefault();

			// Validate form
			if (
				!formData.title.trim() ||
				!formData.instructor.trim() ||
				!formData.category ||
				!formData.description.trim()
			) {
				setUploadState((prev) => ({
					...prev,
					error: 'Please fill in all required fields.',
				}));
				return;
			}

			if (!files.thumbnail || !files.video) {
				setUploadState((prev) => ({
					...prev,
					error: 'Please upload both thumbnail and video files.',
				}));
				return;
			}

			setUploadState({
				isUploading: true,
				error: null,
				success: false,
			});

			try {
				// Filter out empty reflection questions
				const validReflectionQuestions = reflectionQuestions.filter(q => q.trim() !== '');
				
				// Prepare lesson data with URLs
				const lessonData = {
					title: formData.title.trim(),
					instructor: formData.instructor.trim(),
					category: formData.category,
					description: formData.description.trim(),
					thumbnail: files.thumbnail,
					videoUrl: files.video,
					reflectionQuestions: validReflectionQuestions,
				};

				await uploadLessonMutation.mutateAsync(lessonData);

				setUploadState((prev) => ({
					...prev,
					success: true,
				}));

				// Redirect after success
				setTimeout(() => {
					router.push('/lessons');
				}, 2000);
			} catch (error) {
				console.error('Upload error:', error);

				let errorMessage = 'Failed to upload lesson. Please try again.';

				if (error?.response?.data?.message) {
					errorMessage = error.response.data.message;
				}

				setUploadState((prev) => ({
					...prev,
					isUploading: false,
					error: errorMessage,
				}));
			}
		},
		[formData, files, reflectionQuestions, uploadLessonMutation, router],
	);

	return (
		<AdminLayout>
			<div className='min-h-screen bg-gray-50 py-6'>
				<div className='max-w-4xl mx-auto px-6'>
					<div className='flex items-center mb-6'>
						<button
							onClick={handleBack}
							disabled={uploadState.isUploading}
							className='flex items-center gap-2 text-gray-600 cursor-pointer hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'>
							<FaArrowLeft />
							<span className='text-gray-800'>Back</span>
						</button>
					</div>

					<div className='bg-white rounded-lg shadow-lg p-8'>
						<h1 className='text-3xl font-bold text-gray-900 mb-8'>
							Upload New Lesson
						</h1>

						{uploadState.error && (
							<ErrorAlert
								error={uploadState.error}
								onDismiss={dismissError}
							/>
						)}

						<form
							onSubmit={handleSubmit}
							className='space-y-6'>
							{/* Title */}
							<div>
								<label
									htmlFor='title'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Title *
								</label>
								<input
									type='text'
									id='title'
									name='title'
									value={formData.title}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
									placeholder='Enter lesson title'
								/>
							</div>

							{/* Instructor */}
							<div>
								<label
									htmlFor='instructor'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Instructor *
								</label>
								<input
									type='text'
									id='instructor'
									name='instructor'
									value={formData.instructor}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
									placeholder='Enter instructor name'
								/>
							</div>

							{/* Category */}
							<div>
								<label
									htmlFor='category'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Category *
								</label>
								<select
									id='category'
									name='category'
									value={formData.category}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'>
									<option value=''>Select a category</option>
									{categories?.map((category) => (
										<option
											key={category}
											value={category}>
											{category}
										</option>
									))}
								</select>
							</div>

							{/* Description */}
							<div>
								<label
									htmlFor='description'
									className='block text-sm font-medium text-gray-700 mb-2'>
									Description *
								</label>
								<textarea
									id='description'
									name='description'
									value={formData.description}
									onChange={handleInputChange}
									disabled={uploadState.isUploading}
									required
									rows={4}
									className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed resize-none'
									placeholder='Enter lesson description'
								/>
							</div>

							{/* Reflection Questions */}
							<div>
								<div className='flex items-center justify-between mb-4'>
									<label className='block text-sm font-medium text-gray-700'>
										Reflection Questions (Optional)
									</label>
									<button
										type='button'
										onClick={addReflectionQuestion}
										disabled={uploadState.isUploading}
										className='flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors disabled:opacity-50'
									>
										<FaPlus size={12} />
										Add Question
									</button>
								</div>
								<div className='space-y-3'>
									{reflectionQuestions.map((question, index) => (
										<div key={index} className='flex items-center gap-3'>
											<div className='flex-1'>
												<input
													type='text'
													value={question}
													onChange={(e) => handleReflectionQuestionChange(index, e.target.value)}
													disabled={uploadState.isUploading}
													className='w-full px-4 py-3 text-gray-900 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
													placeholder={`Reflection question ${index + 1}`}
												/>
											</div>
											{reflectionQuestions.length > 1 && (
												<button
													type='button'
													onClick={() => removeReflectionQuestion(index)}
													disabled={uploadState.isUploading}
													className='p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50'
												>
													<FaMinus size={14} />
												</button>
											)}
										</div>
									))}
								</div>
								<p className='text-sm text-gray-500 mt-2'>
									Add reflection questions to help users think deeper about the lesson content.
								</p>
							</div>

							{/* File Upload Areas */}
							<div className='grid md:grid-cols-2 gap-6'>
								<CloudinaryUpload
									type='image'
									folder='lesson-thumbnails'
									label='Thumbnail Image'
									onUploadSuccess={handleThumbnailUploadSuccess}
									onUploadError={handleUploadError}
									disabled={uploadState.isUploading}
									required
									value={files.thumbnail}
								/>

								<CloudinaryUpload
									type='video'
									folder='lesson-videos'
									label='Video File'
									onUploadSuccess={handleVideoUploadSuccess}
									onUploadError={handleUploadError}
									disabled={uploadState.isUploading}
									required
									value={files.video}
									maxFileSize={2000000000} // 2GB
								/>
							</div>

							{/* Submit Button */}
							<div className='pt-6'>
								<button
									type='submit'
									disabled={uploadState.isUploading || uploadState.success}
									className='w-full flex items-center cursor-pointer justify-center gap-3 bg-teal-600 hover:bg-teal-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100'>
									{uploadState.isUploading ? (
										<>
											<div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
											<span>Uploading...</span>
										</>
									) : uploadState.success ? (
										<>
											<FaCheckCircle className='text-lg' />
											<span>Upload Complete!</span>
										</>
									) : (
										<>
											<FaUpload className='text-lg' />
											<span>Upload Lesson</span>
										</>
									)}
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</AdminLayout>
	);
}
