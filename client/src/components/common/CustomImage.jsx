/**
 * Enhanced CustomImage Component
 * An improved image component with better error handling, loading states, and optimization
 */
'use client';
import { logo } from '@/src/assets/images';
import Image from 'next/image';
import React, { forwardRef, useState } from 'react';
import { cn } from '@/src/lib/utils';

/**
 * CustomImage component with fallback and loading states
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Alt text for accessibility
 * @param {boolean} props.fill - Whether to fill container
 * @param {number} props.width - Image width
 * @param {number} props.height - Image height
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showLoading - Show loading state
 * @param {React.ReactNode} props.fallback - Custom fallback content
 * @param {Function} props.onLoad - Load handler
 * @param {Function} props.onError - Error handler
 * @param {Object} props.rest - Additional props
 * @returns {React.ReactElement} CustomImage component
 */
const CustomImage = forwardRef(function CustomImage(
	{
		src,
		fill = false,
		className = '',
		alt = '',
		width,
		height,
		showLoading = true,
		fallback,
		onLoad,
		onError,
		...props
	},
	ref,
) {
	const [hasError, setHasError] = useState(false);
	const [isLoading, setIsLoading] = useState(true);

	const fallbackClassName =
		'object-cover object-center h-full w-full bg-gray-200 dark:bg-gray-700';
	const imageSrc = hasError || !src ? logo : src;
	const imageClassName = hasError || !src ? fallbackClassName : className;

	const handleLoad = (e) => {
		setIsLoading(false);
		onLoad?.(e);
	};

	const handleError = (e) => {
		setHasError(true);
		setIsLoading(false);
		onError?.(e);
	};

	return (
		<div className='relative'>
			{isLoading && showLoading && (
				<div
					className={cn(
						'absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800',
						fill ? 'w-full h-full' : `w-[${width}px] h-[${height}px]`,
					)}>
					<div className='animate-pulse bg-gray-300 dark:bg-gray-600 rounded w-8 h-8' />
				</div>
			)}

			{hasError && fallback ? (
				fallback
			) : (
				<Image
					{...props}
					ref={ref}
					src={imageSrc}
					alt={alt || 'Image'}
					className={cn(imageClassName, isLoading && 'opacity-0')}
					width={!fill ? width : undefined}
					height={!fill ? height : undefined}
					fill={fill}
					onLoad={handleLoad}
					onError={handleError}
					priority={props.priority}
				/>
			)}
		</div>
	);
});

CustomImage.displayName = 'CustomImage';

export default CustomImage;
