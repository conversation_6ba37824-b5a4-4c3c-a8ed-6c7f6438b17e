/**
 * Lessons API service
 */
import { api, createQueryString } from './client';

export const lessonsService = {
  /**
   * Get all lessons with pagination and filters
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.category - Filter by category
   * @param {string} params.title - Search by title
   * @returns {Promise} API response
   */
  getLessons: async (params = {}) => {
    const queryString = createQueryString(params);
    const url = queryString ? `/api/lesson/lessons?${queryString}` : '/api/lesson/lessons';
    const response = await api.get(url);
    return response.data;
  },

  /**
   * Get single lesson by ID
   * @param {string} id - Lesson ID
   * @returns {Promise} API response
   */
  getLessonById: async (id) => {
    const response = await api.get(`/api/lesson/${id}`);
    return response.data;
  },

  /**
   * Create new lesson (admin only)
   * @param {Object} lessonData - Lesson data
   * @param {string} lessonData.title - Lesson title
   * @param {string} lessonData.description - Lesson description
   * @param {string} lessonData.category - Lesson category
   * @param {string} lessonData.videoUrl - Video URL
   * @param {Array} lessonData.reflectionQuestions - Reflection questions
   * @returns {Promise} API response
   */
  createLesson: async (lessonData) => {
    const response = await api.post('/api/lesson/lessons', lessonData);
    return response.data;
  },

  /**
   * Update lesson (admin only)
   * @param {string} id - Lesson ID
   * @param {Object} lessonData - Updated lesson data
   * @returns {Promise} API response
   */
  updateLesson: async (id, lessonData) => {
    const response = await api.put(`/api/lesson/${id}`, lessonData);
    return response.data;
  },

  /**
   * Delete lesson (admin only)
   * @param {string} id - Lesson ID
   * @returns {Promise} API response
   */
  deleteLesson: async (id) => {
    const response = await api.delete(`/api/lesson/${id}`);
    return response.data;
  },

  /**
   * Upload lesson video (admin only)
   * @param {FormData} formData - Form data with video file
   * @returns {Promise} API response
   */
  uploadVideo: async (formData) => {
    const response = await api.post('/api/lesson/upload-video', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  /**
   * Get lesson categories
   * @returns {Promise} API response
   */
  getCategories: async () => {
    const response = await api.get('/api/lesson/categories');
    return response.data;
  },

  /**
   * Get featured lessons
   * @param {number} limit - Number of lessons to fetch
   * @returns {Promise} API response
   */
  getFeaturedLessons: async (limit = 6) => {
    const response = await api.get(`/api/lesson/featured?limit=${limit}`);
    return response.data;
  },

  /**
   * Get recent lessons
   * @param {number} limit - Number of lessons to fetch
   * @returns {Promise} API response
   */
  getRecentLessons: async (limit = 10) => {
    const response = await api.get(`/api/lesson/recent?limit=${limit}`);
    return response.data;
  },

  /**
   * Search lessons
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise} API response
   */
  searchLessons: async (query, filters = {}) => {
    const params = { q: query, ...filters };
    const queryString = createQueryString(params);
    const response = await api.get(`/api/lesson/search?${queryString}`);
    return response.data;
  },
};
