/**
 * Consolidated hooks exports
 * This file provides a single entry point for all custom hooks
 */

// Authentication hooks
export {
  useRegister,
  useVerifyEmail,
  useLogin,
  useForgotPassword,
  useVerifyPasswordResetOTP,
  useResetPassword,
  useResendPasswordResetOTP,
  logout,
  getStoredTokens,
  isAuthenticated,
  setHeaderAuthorization,
} from './useAuth';

// User hooks
export {
  useUserProfile,
  useGetAllUsers,
} from './useUser';

// Lessons hooks
export {
  useLessons,
  useLesson,
  useUploadLesson,
  useUpdateLesson,
  useDeleteLesson,
  useCanManageLessons,
  useLessonCategories,
} from './useLessons';

// User progress hooks
export {
  useUserProgress,
  useAllUserProgress,
  useUpdateUserProgress,
  useUpdateReflection,
  useDeleteReflection,
} from './useUserProgress';

// Notification hooks
export {
  useSubscribeToNotifications,
  useUnsubscribeFromNotifications,
  useSendNotification,
  useBroadcastNotification,
  isNotificationSupported,
  isNotificationGranted,
  checkExistingSubscription,
} from './useNotifications';

// Cloudinary upload hook
export {
  useCloudinaryUpload,
} from './useCloudinaryUpload';
