import { useMutation, useQueryClient } from '@tanstack/react-query';
import { authService, setAuthToken } from '../api';

// Re-export for convenience
export const setHeaderAuthorization = setAuthToken;

// Helper function to store tokens
const storeTokens = (tokens) => {
	if (typeof window !== 'undefined') {
		localStorage.setItem('accessToken', tokens.accessToken);
		localStorage.setItem('refreshToken', tokens.refreshToken);
		setAuthToken(tokens.accessToken);
	}
};

// Helper function to clear tokens
const clearTokens = () => {
	if (typeof window !== 'undefined') {
		localStorage.removeItem('accessToken');
		localStorage.removeItem('refreshToken');
		setAuthToken(null);
	}
};

// Register hook
export const useRegister = () => {
	return useMutation({
		mutationFn: authService.register,
		onSuccess: (data) => {
			console.log('Registration initiated:', data);
			// Registration doesn't store tokens yet, only after email verification
		},
		onError: (error) => {
			console.error('Registration error:', error);
		},
	});
};

// Verify email hook
export const useVerifyEmail = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: authService.verifyEmail,
		onSuccess: (data) => {
			console.log('Email verified successfully:', data);
			// Store tokens after successful email verification
			if (data.data?.tokens) {
				storeTokens(data.data.tokens);
				// Invalidate and refetch any user-related queries
				queryClient.invalidateQueries({ queryKey: ['user'] });
			}
		},
		onError: (error) => {
			console.error('Email verification error:', error);
		},
	});
};

// Login hook
export const useLogin = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: authService.login,
		onSuccess: (data) => {
			if (data.data?.tokens) {
				storeTokens(data.data.tokens);
				queryClient.invalidateQueries({ queryKey: ['user'] });
			}
		},
	});
};

// Forgot password hook
export const useForgotPassword = () => {
	return useMutation({
		mutationFn: authService.forgotPassword,
		onSuccess: (data) => {
			console.log('Password reset code sent:', data);
		},
		onError: (error) => {
			console.error('Forgot password error:', error);
		},
	});
};

// Verify password reset OTP hook
export const useVerifyPasswordResetOTP = () => {
	return useMutation({
		mutationFn: authService.verifyPasswordResetOTP,
		onSuccess: (data) => {
			console.log('Password reset OTP verified:', data);
		},
		onError: (error) => {
			console.error('Password reset OTP verification error:', error);
		},
	});
};

// Reset password hook
export const useResetPassword = () => {
	return useMutation({
		mutationFn: authService.resetPassword,
		onSuccess: (data) => {
			console.log('Password reset successful:', data);
		},
		onError: (error) => {
			console.error('Password reset error:', error);
		},
	});
};

// Resend password reset OTP hook
export const useResendPasswordResetOTP = () => {
	return useMutation({
		mutationFn: authService.resendOTP,
		onSuccess: (data) => {
			console.log('Password reset OTP resent:', data);
		},
		onError: (error) => {
			console.error('Resend password reset OTP error:', error);
		},
	});
};

// Logout function (utility function, not a hook)
export const logout = () => {
	clearTokens();
	if (typeof window !== 'undefined') {
		window.dispatchEvent(new Event('auth-change'));
	}
};

// Get stored tokens (utility function)
export const getStoredTokens = () => {
	if (typeof window !== 'undefined') {
		return {
			accessToken: localStorage.getItem('accessToken'),
			refreshToken: localStorage.getItem('refreshToken'),
		};
	}
	return { accessToken: null, refreshToken: null };
};

// Check if user is authenticated (utility function)
export const isAuthenticated = () => {
	const { accessToken } = getStoredTokens();
	return !!accessToken;
};
