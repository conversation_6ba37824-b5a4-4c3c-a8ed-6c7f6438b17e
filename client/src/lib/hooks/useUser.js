import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userService, setAuthToken } from '../api';
import { isAuthenticated, logout } from './useAuth';

// Get user profile hook
export const useUserProfile = () => {
	return useQuery({
		queryKey: ['user', 'profile'],
		queryFn: async () => {
			// Ensure we have the latest token in the header before making the request
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await userService.getProfile();
		},
		enabled: isAuthenticated(), // Only fetch if user is authenticated
		staleTime: 5 * 60 * 1000, // 5 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
		retry: (failureCount, error) => {
			// Don't retry on 401 errors (unauthorized)
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching user profile:', error);
				// Handle 401 errors by logging out (also handled by interceptor)
				if (error?.response?.status === 401) {
					logout();
					// Dispatch custom event for auth change
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
			onSuccess: (data) => {
				console.log('User profile fetched successfully:', data);
			},
		},
	});
};

export const useGetAllUsers = ({ page = 1, limit = 10, search = '' } = {}) => {
	return useQuery({
		queryKey: ['users', 'all', page, limit, search],
		queryFn: async () => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setAuthToken(token);
			}
			return await userService.getAllUsers({ page, limit, search });
		},
		enabled: isAuthenticated(),
		staleTime: 5 * 60 * 1000,
		gcTime: 10 * 60 * 1000,
		retry: (failureCount, error) => {
			// Don't retry on 401 errors (unauthorized)
			if (error?.response?.status === 401) {
				return false;
			}
			return failureCount < 3;
		},
		meta: {
			onError: (error) => {
				console.error('Error fetching users:', error);
				if (error?.response?.status === 401) {
					logout();
					// Dispatch custom event for auth change
					if (typeof window !== 'undefined') {
						window.dispatchEvent(new Event('auth-change'));
					}
				}
			},
			onSuccess: (data) => {
				console.log('Users fetched successfully:', data);
			},
		},
	});
};

// Update user status hook
export const useUpdateUserStatus = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ userId, status }) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setHeaderAuthorization(token);
			}
			return await userAPI.updateUserStatus(userId, status);
		},
		onSuccess: (data) => {
			// Invalidate users queries to refetch the data
			queryClient.invalidateQueries({ queryKey: ['users'] });
			console.log('User status updated successfully:', data);
		},
		onError: (error) => {
			console.error('Error updating user status:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};

// Delete user hook
export const useDeleteUser = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (userId) => {
			const token = localStorage.getItem('accessToken');
			if (token) {
				setHeaderAuthorization(token);
			}
			return await userAPI.deleteUser(userId);
		},
		onSuccess: (data) => {
			// Invalidate users queries to refetch the data
			queryClient.invalidateQueries({ queryKey: ['users'] });
			console.log('User deleted successfully:', data);
		},
		onError: (error) => {
			console.error('Error deleting user:', error);
			if (error?.response?.status === 401) {
				logout();
				if (typeof window !== 'undefined') {
					window.dispatchEvent(new Event('auth-change'));
				}
			}
		},
	});
};
